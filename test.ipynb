{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'system': \"Your role as an assistant involves thoroughly exploring questions through a systematic long thinking process before providing the final precise and accurate solutions. This requires engaging in a comprehensive cycle of analysis, summarizing, exploration, reassessment, reflection, backtracing, and iteration to develop well-considered thinking process. Please structure your response into two main sections: Thought and Solution. In the Thought section, detail your reasoning process using the specified format: <|begin_of_thought|> {thought with steps separated with '\\\\n\\\\n'} <|end_of_thought|> Each step should include detailed considerations such as analisying questions, summarizing relevant findings, brainstorming new ideas, verifying the accuracy of the current steps, refining any errors, and revisiting previous steps. In the Solution section, based on various attempts, explorations, and reflections from the Thought section, systematically present the final solution that you deem correct. The solution should remain a logical, accurate, concise expression style and detail necessary step needed to reach the conclusion, formatted as follows: <|begin_of_solution|> {final formatted, precise, and clear solution} <|end_of_solution|> Now, try to solve the following question through the above guidelines:\", 'conversations': [{'from': 'user', 'value': 'Generate an executable Python function generated from the given prompt. The function should take stdin as input and print the output. Simply call the function after the definition.The Chef likes to stay in touch with his staff. So, the Chef, the head server, and the sous-chef all carry two-way transceivers so they can stay in constant contact. Of course, these transceivers have a limited range so if two are too far apart, they cannot communicate directly.\\n\\n\\nThe Chef invested in top-of-the-line transceivers which have a few advanced features. One is that even if two people cannot talk directly because they are out of range, if there is another transceiver that is close enough to both, then the two transceivers can still communicate with each other using the third transceiver as an intermediate device.\\n\\n\\nThere has been a minor emergency in the Chef\\'s restaurant\\nand he needs to communicate with both the head server and the sous-chef right away. Help the Chef determine if it is possible for all three people to communicate with each other, even if two must communicate through the third because they are too far apart.\\n\\n\\nInput\\n\\nThe first line contains a single positive integer T ≤ 100 indicating the number of test cases to follow. The first line of each test case contains a positive integer R ≤ 1,000 indicating that two transceivers can communicate directly without an intermediate transceiver if they are at most R meters away from each other. The remaining three lines of the test case describe the current locations of the Chef, the head server, and the sous-chef, respectively. Each such line contains two integers X,Y (at most 10,000 in absolute value) indicating that the respective person is located at position X,Y.\\n\\n\\nOutput\\n\\nFor each test case you are to output a single line containing a single string. If it is possible for all three to communicate then you should output \"yes\". Otherwise, you should output \"no\".\\n\\n\\nTo be clear, we say that two transceivers are close enough to communicate directly if the length of the straight line connecting their X,Y coordinates is at most R.\\n\\n\\nExample\\n\\nInput:\\n3\\n1\\n0 1\\n0 0\\n1 0\\n2\\n0 1\\n0 0\\n1 0\\n2\\n0 0\\n0 2\\n2 1\\n\\n\\nOutput:\\nyes\\nyes\\nno\\n'}, {'from': 'assistant', 'value': '<|begin_of_thought|>\\n\\nOkay, let\\'s see. I need to solve this problem where three people (Chef, head server, sous-chef) have transceivers that can communicate directly if within R meters. If not, but there\\'s a third person acting as a bridge, then they can still communicate. The goal is to check if all three can communicate with each other, possibly through the third.\\n\\nHmm. So for each test case, given R and three points, determine if the three form a connected graph where each pair is either directly connected (distance <= R) or connected via the third.\\n\\nWait, but how exactly? Let\\'s think. The communication can go through one intermediary. So all three must be in a chain where each consecutive pair is within R, or perhaps any two are connected via a path through the third.\\n\\nWait, the problem says: if two can\\'t talk directly but there\\'s a third that is close enough to both, then they can communicate through that third. So the communication graph must be connected. But the three people can form a triangle where each is connected to the others directly, or there\\'s a central node that connects the other two.\\n\\nSo the condition is that all three can communicate with each other, possibly through one another. So the overall structure must allow for any two of them to communicate, either directly or via the third.\\n\\nBut how do I model this? Let\\'s think in terms of possible scenarios where the three can communicate:\\n\\nCase 1: All three are pairwise within R. Then yes.\\n\\nCase 2: Two pairs are within R. For example, Chef to head server is within R, head server to sous-chef is within R. Then Chef and sous-chef can communicate through the head server. So all three can communicate.\\n\\nCase 3: Only one pair is within R, but the third is connected to both via that pair. Wait, no. For example, Chef and head server are within R. Chef and sous-chef are not. Head server and sous-chef are not. Then can the sous-chef communicate with Chef through the head server? Well, sous-chef would need to be within R of the head server, but that\\'s not the case here. Wait, no. So if the head server is connected to Chef (distance <= R), and sous-chef is connected to the head server (distance <= R), then yes. But if the sous-chef is connected to neither, but connected via the Chef and head server? No. Wait, no. Because if the sous-chef is only connected to the Chef, but the Chef is connected to the head server, then sous-chef can communicate with the head server via Chef. But all three would need to form a connected graph.\\n\\nWait, perhaps the correct approach is to model the three points as a graph where edges exist between pairs if their distance is <= R. Then, check if the graph is connected. If the graph is connected, then all three can communicate.\\n\\nYes, that\\'s right. Because if the graph is connected, then all nodes are reachable via some path. For three nodes, the possible connected graphs are:\\n\\n- All three nodes connected directly (triangle).\\n- A chain (A connected to B, B connected to C). Then A and C can communicate via B.\\n\\nSo for the problem, we need to check if the three points form a connected graph in this sense.\\n\\nSo the steps for each test case would be:\\n\\n1. Calculate the pairwise distances between all three points.\\n2. For each pair, determine if the distance is <= R. If yes, that pair is connected.\\n3. Then check if the graph formed by these edges is connected. If yes, output \\'yes\\', else \\'no\\'.\\n\\nBut how to check if the graph is connected with three nodes?\\n\\nWell, for three nodes, the graph is connected if there\\'s at least two edges such that all three are connected. For example:\\n\\nIf there are two edges (A-B and B-C), then connected. If all three have edges (A-B, B-C, A-C), connected. If two edges (A-B and A-C), connected.\\n\\nAlternatively, if there\\'s at least two edges and they form a connected structure. So another approach is to check if all three are reachable via the edges.\\n\\nBut with three nodes, perhaps it\\'s easier to check all possible conditions.\\n\\nAlternatively, for three nodes, the graph is connected if:\\n\\nEither all three are connected (each pair has an edge), or there are two edges that connect all three (like a chain), or any two edges that form a connected graph.\\n\\nWait, in three nodes, the graph is connected if:\\n\\n- There are at least two edges, and the edges form a path that connects all three.\\n\\nAlternatively, the connected graph can be checked by seeing if any one node can reach the other two via the edges.\\n\\nBut perhaps for three nodes, it\\'s easier to check all possible possibilities.\\n\\nSo possible scenarios where the graph is connected:\\n\\n1. All three pairs have distance <= R. So all three edges exist. Then yes.\\n\\n2. Exactly two edges exist, and they form a chain. For example, A connected to B, B connected to C. Then A can reach C via B. So yes.\\n\\n3. Exactly one edge exists. Then only two nodes are connected, the third is isolated. So the graph is disconnected. So no.\\n\\n4. If two edges exist but not forming a chain, like A connected to B, A connected to C. Then the graph is connected. Because B and C can communicate through A.\\n\\nSo in this case, if any two of the three pairs are connected, and there exists a third node that connects to at least one of them, then the graph is connected.\\n\\nWait, no. Let\\'s think: if A is connected to B and A is connected to C, then B can reach C through A. So the graph is connected.\\n\\nSo for three nodes, the graph is connected if the number of edges is >=2, and the edges are such that all three are connected. Which for three nodes, if there are two edges, then it\\'s possible to have a connected graph.\\n\\nSo the possible conditions for the graph to be connected are:\\n\\n- There are at least two edges, and all three nodes are connected via those edges.\\n\\nWhich can be checked by:\\n\\nEither:\\n\\n- All three nodes are connected via their edges (i.e., the edges form a triangle or a chain).\\n\\nAlternatively, for three nodes, the graph is connected if there exists a path between every pair of nodes. But with three nodes, that\\'s possible if the graph is a triangle, a chain, or any two edges.\\n\\nWait, perhaps the easiest way is to check if all three nodes are in the same connected component.\\n\\nHow to compute that for three nodes?\\n\\nWell, let\\'s see. For three nodes, there are three possible pairs: AB, BC, CA.\\n\\nWe can represent the edges as a graph, then check if all three nodes are reachable from each other.\\n\\nBut for three nodes, the connectedness can be determined by checking if the union of the edges connects all three.\\n\\nBut maybe it\\'s easier to consider the possible cases:\\n\\nCase 1: All three pairs are within R. Then connected.\\n\\nCase 2: Exactly two pairs are within R. Then check if the two edges form a connected graph.\\n\\nFor example, if AB and BC are connected: then the graph is connected.\\n\\nIf AB and AC are connected: then the graph is connected (since B and C can communicate via A).\\n\\nIf AB and CA are connected: same as above.\\n\\nCase 3: Exactly one pair is within R. Then the third node is disconnected, so no.\\n\\nCase 4: None of the pairs are within R. Then no.\\n\\nSo for the code, perhaps the steps are:\\n\\nFor each test case:\\n\\n1. Read R.\\n\\n2. Read the three points (A, B, C).\\n\\n3. Compute the pairwise distances between A-B, B-C, C-A.\\n\\n4. For each pair, determine if the distance is <= R. So check AB <= R, BC <= R, CA <= R.\\n\\n5. Now, check if the three nodes form a connected graph.\\n\\nThe connected graph is possible if:\\n\\nEither:\\n\\n- All three pairs are connected (AB, BC, CA all <= R). Then yes.\\n\\nOR\\n\\n- Any two pairs are connected, and they share a common node (so forming a triangle or a chain). For example, AB and BC connected (sharing B), AB and AC connected (sharing A), etc.\\n\\nOR\\n\\n- All three can communicate through a central node. For example, AB and AC are connected (A is the central node), so B and C can communicate via A. So in this case, the graph is connected.\\n\\nAlternatively, if two pairs are connected and those two pairs share a node (so the central node), then the graph is connected.\\n\\nSo for two edges:\\n\\nIf two edges share a common node (like AB and AC), then connected.\\n\\nIf the two edges are AB and BC, then connected (chain).\\n\\nBut if two edges are AB and CA, then it\\'s possible. Wait, AB is between A and B. CA is between C and A. So A is connected to both B and C. So B and C can communicate via A. So yes.\\n\\nSo any two edges in the three-node graph will form a connected graph, because with three nodes, two edges must share at least one node (since there are three possible edges). Wait, no. For example, AB and BC: two edges, share B. AB and AC: two edges, share A. AB and CA: AB is A-B, CA is C-A. So A is connected to B and C. So all three are connected.\\n\\nBut what if two edges are AB and CD (but CD is not part of our three nodes). Wait, no, the three nodes are A, B, C. So any two edges must be between pairs of these three.\\n\\nSo, in three nodes, any two edges will form a connected graph. Because in three nodes, two edges can\\'t form two separate edges that are not connected. Let me think: for example, if the edges are AB and BC: connected. If edges are AB and AC: connected. What if edges are AB and CD? Not possible. So in three nodes, two edges will always form a connected graph. Because the two edges have to connect two pairs, but given that there are three nodes, two edges must form a chain or a fork.\\n\\nWait, three nodes, two edges. The possible combinations are:\\n\\n- AB and BC: forms a chain A-B-C. Connected.\\n\\n- AB and AC: forms a fork (A connected to B and C). B and C can communicate via A. Connected.\\n\\n- AB and CD: but CD is not a pair of the three nodes. So no, that\\'s not possible.\\n\\nSo yes, for three nodes, if there are two edges, the graph is connected. So the graph is connected if there are two or three edges.\\n\\nWait, what if there are two edges between AB and BC: yes, connected. If two edges between AB and AC: connected. If two edges between AB and BC: connected. So yes, any two edges in three nodes will form a connected graph.\\n\\nSo the condition is:\\n\\nIf the number of edges (each edge is a pair with distance <= R) is >= 2, then the graph is connected.\\n\\nOR\\n\\nIf the graph has two edges, then connected.\\n\\nWait, but what about three nodes with two edges: AB and BC. So A is connected to B, B connected to C. Then the graph is connected. So yes.\\n\\nIf two edges are AB and CD (but that\\'s not possible here). So in our problem, the three nodes are A, B, C. So any two edges must be between two of these three.\\n\\nSo, the conclusion is that for three nodes, the graph is connected if:\\n\\nThe number of edges (edges where distance <= R) is >= 2.\\n\\nWait, no. Wait, if two edges are AB and BC, then yes. If two edges are AB and AC, then yes. But what if two edges are AB and CD (but CD is not part of the nodes). So for our problem, since the nodes are A, B, C, the edges can only be AB, BC, CA.\\n\\nSo, for three nodes, if there are two edges, the graph is connected.\\n\\nBecause any two edges must connect all three nodes. For example:\\n\\nAB and BC: A connected to B connected to C. All three connected.\\n\\nAB and AC: A is connected to B and C, so B and C can communicate through A.\\n\\nAB and CA: same as AB and AC.\\n\\nBC and CA: B connected to C connected to A. So all three connected.\\n\\nSo in all cases, two edges in three nodes form a connected graph.\\n\\nSo the condition for the graph being connected is:\\n\\nIf the number of edges (AB, BC, CA) with distance <= R is >= 2.\\n\\nOR if there\\'s at least one edge that connects all three.\\n\\nWait, but what if there\\'s exactly two edges, but they are AB and BC. Then all three are connected. If there are two edges AB and AC, then all three are connected.\\n\\nSo the conclusion is that for three nodes, the graph is connected if the number of edges (distance <= R) is >= 2, OR there exists at least two edges that form a connected structure.\\n\\nWait, but with two edges, it\\'s always connected. So in that case, the answer is yes.\\n\\nBut what if there\\'s only one edge? Then two nodes are connected, the third is not. So the graph is disconnected.\\n\\nSo the possible scenarios where the answer is \\'yes\\' are:\\n\\nEither:\\n\\n- All three are connected (three edges).\\n\\nOR\\n\\n- Two edges (so two pairs are connected, but in any configuration).\\n\\nOR\\n\\n- One edge and another edge that connects the third via the other. Wait, no. If there\\'s only one edge, then the third node is isolated.\\n\\nSo the answer is \\'yes\\' if:\\n\\nThe three nodes form a connected graph, which for three nodes is equivalent to:\\n\\nAt least two of the three possible edges exist.\\n\\nOR\\n\\nOne of the nodes is connected to both of the other two, even if those two are not connected directly.\\n\\nWait, no. Because if node A is connected to B and C (two edges), but B and C are not connected. Then all three can communicate via A. So in this case, the graph is connected. So two edges (AB and AC) are sufficient.\\n\\nSo yes, the code can check if the number of edges is >=2. Because two edges in three nodes will form a connected graph.\\n\\nWait, but what about if two edges are AB and BC. Then that\\'s two edges, and the graph is connected. If two edges are AB and AC, the graph is connected. So any two edges in three nodes will form a connected graph.\\n\\nSo the condition is: if the number of edges (distance <= R) is >=2, then output \\'yes\\'. Otherwise, output \\'no\\'.\\n\\nWait, but there\\'s another possibility. Suppose that all three pairs are not connected (all distances > R). Then output \\'no\\'.\\n\\nIf exactly one edge exists, then two nodes are connected, third is isolated. So output \\'no\\'.\\n\\nIf two edges exist, output \\'yes\\'.\\n\\nIf three edges exist, output \\'yes\\'.\\n\\nSo the logic is:\\n\\ncount = number of pairs with distance <= R.\\n\\nif count >= 2: output \\'yes\\'\\n\\nelse: output \\'no\\'\\n\\nIs that correct?\\n\\nWait, let\\'s test with the examples.\\n\\nFirst example input:\\n\\n3\\n1\\n0 1\\n0 0\\n1 0\\n\\nSo R=1.\\n\\nPoints:\\n\\nChef is (0,1), head server (0,0), sous-chef (1,0).\\n\\nCompute distances:\\n\\nChef to head: sqrt((0-0)^2 + (1-0)^2) = 1. So <=1. So edge exists.\\n\\nHead to sous-chef: sqrt( (0-1)^2 + (0-0)^2 ) = 1. Edge exists.\\n\\nSous-chef to Chef: sqrt( (0-1)^2 + (1-0)^2 ) = sqrt(2) ≈1.414>1. So edge does not exist.\\n\\nSo edges are AB and BC (count=2). So count >=2 → output yes.\\n\\nWhich matches the first test case\\'s output.\\n\\nSecond test case:\\n\\nR=2.\\n\\nSame points.\\n\\nChef to head: 1 <=2 → edge exists.\\n\\nHead to sous-chef: 1 <=2 → edge exists.\\n\\nSous-chef to Chef: sqrt(2) ≈1.414 <=2 → yes. So three edges. Count=3 → output yes. Which matches the sample.\\n\\nThird test case:\\n\\nR=2.\\n\\nChef at (0,0), head at (0,2), sous-chef at (2,1).\\n\\nCompute distances:\\n\\nChef to head: sqrt( (0-0)^2 + (0-2)^2 ) = 2 → which is <=2. Edge exists.\\n\\nHead to sous-chef: sqrt( (0-2)^2 + (2-1)^2 ) = sqrt(4+1)=sqrt(5)≈2.236>2 → no edge.\\n\\nSous-chef to Chef: sqrt( (0-2)^2 + (0-1)^2 ) = sqrt(4+1) = sqrt(5) >2 → no edge.\\n\\nSo edges are AB. Count=1. So output no. Which matches the third test case.\\n\\nSo the logic seems to work.\\n\\nAnother test case: two edges. Suppose Chef is connected to head and sous-chef. Then count=2. So output yes.\\n\\nAnother case: two edges. Suppose Chef connected to head, and sous-chef connected to Chef. Then count=2. Output yes.\\n\\nSo the code can check whether the number of edges (distance <= R) is at least two. If yes, output yes. Else, no.\\n\\nBut wait, what if two edges are AB and AC. Then count=2. Output yes.\\n\\nYes. Because A can act as a bridge between B and C.\\n\\nSo the code can proceed as follows:\\n\\nFor each test case:\\n\\n1. Read R.\\n\\n2. Read three points.\\n\\n3. Compute the three pairwise distances.\\n\\n4. For each pair, check if distance <= R. Increment count for each such pair.\\n\\n5. If count >=2, output yes. Else, no.\\n\\nBut wait, is there any case where count >=2 but the graph is not connected?\\n\\nWait, suppose there are two edges, but they are AB and CD (but CD is not part of the nodes). But that\\'s impossible here. All edges are between the three nodes.\\n\\nSo with three nodes, two edges will always form a connected graph. So the count >=2 implies the graph is connected.\\n\\nSo yes, the code can be written as:\\n\\nif number of edges (pairs with distance <= R) >=2 → yes else no.\\n\\nBut wait, let\\'s think of a case where there are two edges, but the third node is not connected to either.\\n\\nWait, for example:\\n\\nThree nodes A, B, C.\\n\\nEdges AB and BC. So A is connected to B, B connected to C. So all three are connected. So yes.\\n\\nAnother example: edges AB and AC. So A is connected to B and C. So all three are connected.\\n\\nSo no way to have two edges and a disconnected graph.\\n\\nThus, the code can safely check if the count of edges (pairs with distance <= R) is >=2. If yes, output yes, else no.\\n\\nSo the code can be written as:\\n\\nFor each test case:\\n\\nCalculate the three pairwise distances.\\n\\nCheck each pair.\\n\\nIf two or more pairs have distance <= R → yes.\\n\\nElse → no.\\n\\nSo that\\'s the approach.\\n\\nNow, code steps:\\n\\nRead T test cases.\\n\\nFor each test case:\\n\\nRead R.\\n\\nRead three lines, each line has X and Y for Chef, head, sous-chef.\\n\\nCompute the three pairs: Chef-head, head-sous, sous-chef.\\n\\nFor each pair, compute the distance squared, compare to R squared (to avoid floating point operations).\\n\\nWait, but the problem says the distance should be <= R. So for example, if the distance is exactly R, it\\'s allowed.\\n\\nSo for each pair (x1,y1) and (x2,y2):\\n\\ndx = x1 - x2\\n\\ndy = y1 - y2\\n\\ndistance squared is dx*dx + dy*dy.\\n\\nIf distance squared <= R*R → yes for that pair.\\n\\nSo this avoids floating point inaccuracies.\\n\\nSo code steps:\\n\\nFor each pair:\\n\\nCalculate dx = x1 - x2\\n\\ndy = y1 - y2\\n\\ndist_sq = dx*dx + dy*dy\\n\\nif dist_sq <= R_squared (R is given, so R_squared = R*R)\\n\\nThen count +=1.\\n\\nThen, if the total count >=2 → yes else no.\\n\\nSo code in Python:\\n\\nRead T.\\n\\nFor each T times:\\n\\nRead R.\\n\\nRead three lines for the three points.\\n\\nCompute all three pairs.\\n\\nCheck how many pairs have distance squared <= R^2.\\n\\nIf >=2 → yes else no.\\n\\nWait, but the three pairs are Chef and head, head and sous, sous and Chef.\\n\\nWait, the three pairs are:\\n\\nChef (A) and head (B) → AB.\\n\\nHead (B) and sous (C) → BC.\\n\\nSous (C) and Chef (A) → CA.\\n\\nSo for each test case, three pairs.\\n\\nSo code:\\n\\nRead T.\\n\\nFor each T:\\n\\nR = int(input())\\n\\nA = list(map(int, input().split()))\\nB = list(map(int, input().split()))\\nC = list(map(int, input().split()))\\n\\ncount = 0\\n\\n# AB\\ndx = A[0] - B[0]\\ndy = A[1] - B[1]\\ndist_sq_ab = dx*dx + dy*dy\\nif dist_sq_ab <= R*R:\\n    count +=1\\n\\n# BC\\ndx = B[0] - C[0]\\ndy = B[1] - C[1]\\ndist_sq_bc = dx*dx + dy*dy\\nif dist_sq_bc <= R*R:\\n    count +=1\\n\\n# CA\\ndx = C[0] - A[0]\\ndy = C[1] - A[1]\\ndist_sq_ca = dx*dx + dy*dy\\nif dist_sq_ca <= R*R:\\n    count +=1\\n\\nif count >=2:\\n    print(\"yes\")\\nelse:\\n    print(\"no\")\\n\\nWait, but what if two pairs are connected, but not all three. For example, AB and BC are connected, but CA is not. So count is 2 → yes. Which is correct.\\n\\nAnother example: AB and AC are connected. So count is 2 → yes. Then, all three can communicate through A.\\n\\nYes.\\n\\nAnother case: all three pairs are connected. Count is 3 → yes.\\n\\nSo this code should handle all cases.\\n\\nTesting the sample input:\\n\\nSample input:\\n\\n3\\n1\\n0 1 → A\\n0 0 → B\\n1 0 → C\\n\\nFor AB: (0-0)^2 + (1-0)^2 = 1 → 1 <= 1 → yes. count=1.\\n\\nBC: (0-1)^2 + (0-0)^2 → 1 → yes. count=2.\\n\\nCA: (0-1)^2 + (1-0)^2 → 2 → 2>1 → no. So count is 2 → yes.\\n\\nSample output is yes.\\n\\nSecond test case:\\n\\nR=2.\\n\\nSame points.\\n\\nAB: 1 <=4 → yes. count=1.\\n\\nBC: 1 <=4 → yes. count=2.\\n\\nCA: 2 <=4 → yes. count=3 → yes.\\n\\nThird test case:\\n\\nR=2.\\n\\nA (0,0), B(0,2), C(2,1).\\n\\nAB: (0-0)^2 + (0-2)^2 =4 → 4 <=4 → yes. count=1.\\n\\nBC: (0-2)^2 + (2-1)^2 →4+1=5>4 → no.\\n\\nCA: (2-0)^2 + (1-0)^2 =4+1=5>4 → no. So count=1 → output no.\\n\\nWhich matches the sample.\\n\\nSo this code should work.\\n\\nNow, code structure:\\n\\nThe function needs to read from stdin and print the output. So the code can be written as:\\n\\nRead T.\\n\\nLoop T times:\\n\\nRead R.\\n\\nRead three lines for the three points.\\n\\nCompute the three distances squared.\\n\\nCheck how many are <= R squared.\\n\\nIf count >=2 → yes, else no.\\n\\nThus, the code can be written as a function that reads input and outputs as per the problem.\\n\\nSo the code would look like:\\n\\ndef main():\\n    import sys\\n    input = sys.stdin.read().split()\\n    ptr = 0\\n    T = int(input[ptr])\\n    ptr +=1\\n    for _ in range(T):\\n        R = int(input[ptr])\\n        ptr +=1\\n        A = list(map(int, input[ptr:ptr+2]))\\n        ptr +=2\\n        B = list(map(int, input[ptr:ptr+2]))\\n        ptr +=2\\n        C = list(map(int, input[ptr:ptr+2]))\\n        ptr +=2\\n        count = 0\\n        # AB\\n        dx = A[0] - B[0]\\n        dy = A[1] - B[1]\\n        if dx*dx + dy*dy <= R*R:\\n            count +=1\\n        # BC\\n        dx = B[0] - C[0]\\n        dy = B[1] - C[1]\\n        if dx*dx + dy*dy <= R*R:\\n            count +=1\\n        # CA\\n        dx = C[0] - A[0]\\n        dy = C[1] - A[1]\\n        if dx*dx + dy*dy <= R*R:\\n            count +=1\\n        print(\"yes\" if count >=2 else \"no\")\\n\\nmain()\\n\\nBut the problem says that the function should take stdin as input and print the output. So the code reads from stdin, processes, and prints.\\n\\nYes.\\n\\nThis should handle all cases correctly.\\n\\nAnother test case: where two pairs are connected but through a third.\\n\\nFor example:\\n\\nR=5.\\n\\nA (0,0), B (0,5), C (0,10).\\n\\nAB distance is 5 → yes.\\n\\nBC distance is5 → yes.\\n\\nCA distance is10 → no.\\n\\nCount is 2 → output yes. So all three can communicate through B.\\n\\nSo the code works.\\n\\nAnother test case where only one edge exists:\\n\\nR=5.\\n\\nA (0,0), B (0,6), C (0,5).\\n\\nAB distance 6>5 → no.\\n\\nBC distance 1 → yes.\\n\\nCA distance 5 → yes.\\n\\nSo count is 2 → yes. Because BC and CA are connected. So B and C are connected, C and A are connected. So B can reach A via C.\\n\\nThus, the code works.\\n\\n<|end_of_thought|>\\n\\n<|begin_of_solution|>\\n\\nTo solve this problem, we need to determine if three people (Chef, head server, and sous-chef) can communicate with each other using transceivers that have a limited range. Communication can be direct if they are within the range or indirect via another person acting as an intermediary.\\n\\n### Approach\\n1. **Input Reading**: Read the number of test cases and for each test case, read the maximum communication range (R) and the coordinates of the three people.\\n2. **Distance Calculation**: For each pair of people, calculate the squared distance between their coordinates to avoid floating-point inaccuracies.\\n3. **Check Communication**: Determine how many pairs can communicate directly by comparing their squared distance with \\\\( R^2 \\\\).\\n4. **Connected Graph Check**: If at least two pairs can communicate directly, the three people form a connected graph either directly or through an intermediary, allowing all to communicate.\\n\\n### Solution Code\\n```python\\ndef main():\\n    import sys\\n    input = sys.stdin.read().split()\\n    ptr = 0\\n    T = int(input[ptr])\\n    ptr += 1\\n    for _ in range(T):\\n        R = int(input[ptr])\\n        ptr += 1\\n        A = list(map(int, input[ptr:ptr+2]))\\n        ptr += 2\\n        B = list(map(int, input[ptr:ptr+2]))\\n        ptr += 2\\n        C = list(map(int, input[ptr:ptr+2]))\\n        ptr += 2\\n        count = 0\\n        \\n        # Check Chef to Head Server\\n        dx = A[0] - B[0]\\n        dy = A[1] - B[1]\\n        if dx * dx + dy * dy <= R * R:\\n            count += 1\\n        \\n        # Check Head Server to Sous-Chef\\n        dx = B[0] - C[0]\\n        dy = B[1] - C[1]\\n        if dx * dx + dy * dy <= R * R:\\n            count += 1\\n        \\n        # Check Sous-Chef to Chef\\n        dx = C[0] - A[0]\\n        dy = C[1] - A[1]\\n        if dx * dx + dy * dy <= R * R:\\n            count += 1\\n        \\n        print(\"yes\" if count >= 2 else \"no\")\\n\\nmain()\\n```\\n\\n### Explanation\\n- **Reading Input**: The input is read all at once and split into a list for efficient processing.\\n- **Distance Calculation**: For each pair of points (Chef-Head, Head-Sous, Sous-Chef), the squared distance is computed and compared against \\\\( R^2 \\\\).\\n- **Communication Check**: The count of valid direct communication pairs is determined. If this count is 2 or more, it ensures that all three can communicate either directly or through an intermediary, thus printing \"yes\". Otherwise, it prints \"no\".\\n\\nThis approach efficiently checks the necessary conditions using integer arithmetic to avoid precision issues, ensuring correctness and optimal performance.\\n\\n<|end_of_solution|>'}]}\n"]}], "source": ["from datasets import load_dataset\n", "\n", "# 定义文件路径（注意Windows路径需双反斜杠）\n", "base_path = \"/mnt/d/hgmodels/datasets/OpenThoughts-114k/\"\n", "\n", "# 加载 main 数据集\n", "dataset_main = load_dataset(base_path)\n", "ds = dataset_main['train']\n", "print(ds[0])\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["好的，让我们来解决一个高中难度的数学问题，这个问题涉及到三角函数中的余切（cot）。\n", "问题：已知 cotθ= 3/4，且 �� 是一个锐角。求 sinθ 和 cosθ 的值。请给出具体解题步骤和结果。 \n", "\n", "首先，我们需要明确余弦和正切的关系：\n", "- ��切是余弦的倒数\n", "- 余弦是正切的倒数\n", "\n", "所以对于给定的 cotθ = 3/4，我们可以表示为 tanθ = 1/3。\n", "\n", "接下来，我们要找到 sinθ 和 cosθ 的值。\n", "\n", "根据正切与余弦的关系：\n", "\n", "tanθ = 1/3\n", "\n", "设 tanθ = 1/k, ���么 k =3，所以 tanθ = 1/3。\n", "\n", "然后我们可以通过勾股定理计算sinθ 和 cosθ。\n", "\n", "假设斜边长度为 r，那么根据勾股定理：\n", "\n", "r² = a² + b²\n", "\n", "其中，a = x (cosine), b = y (sine)\n", "\n", "因此，可以得到：\n", "\n", "x² + y² = r²\n", "\n", "因为 r² = x² + y²\n", "\n", "所以，\n", "\n", "y² = r² - x²\n", "\n", "又因为 tanθ = 1/k = y/r = 1/(k*r) = 1/(3*r)\n", "\n", "所以 y/r = 1/(3*r)\n", "\n", "所以 y = r /3\n", "\n", "将 y = r /3 代入到 y² = r² -x² 中：\n", "\n", "(r/3)^2 = r² -x²\n", "\n", "整理得：\n", "\n", "(1/9)r² = r² -x²\n", "\n", "=> x² = 8r² /9\n", "\n", "所以 x = sqrt(8r² /9) = sqrt(8)/3 * r = sqrt(2*4)/3 * r = sqrt(8)/3 * r = sqrt(2)*sqrt(2)/3 * r = sqrt(4)/3 * r = 2/r\n", "\n", "但是这个表达式看起来有些复杂，因为我们需要先确定 r，然后再计算 x 和 y。\n", "\n", "但为了简化计算，我们可以考虑两个方向：\n", "\n", "一种方法是直接利用tanθ的定义：\n", "\n", "tanθ = y/r = 1/3\n", "\n", "所以 y = 3*tanθ = 3*(1/3) = 1.\n", "\n", "于是 r^2 = x\n"]}], "source": ["from transformers import AutoTokenizer\n", "from transformers import AutoModelForCausalLM\n", "\n", "model_name = \"output/Qwen-0.5B-GRPO/checkpoint-1800/\"\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForCausalLM.from_pretrained(model_name)\n", "\n", "msg = \"\"\"好的，让我们来解决一个高中难度的数学问题，这个问题涉及到三角函数中的余切（cot）。\n", "问题：已知 cotθ= 3/4，且 θ 是一个锐角。求 sinθ 和 cosθ 的值。请给出具体解题步骤和结果。\"\"\"\n", "\n", "inputs = tokenizer(msg, return_tensors=\"pt\")\n", "# 模型推理\n", "# 设置最大生成长度（可根据需要调整）\n", "max_length = 500  # 最大生成长度\n", "generated_tokens = model.generate(\n", "    **inputs,\n", "    max_length=max_length,\n", "    streamer=None  # 不使用 streamer，手动实现流式输出\n", ")\n", "\n", "# 流式打印生成的结果\n", "for token_id in generated_tokens[0]:\n", "    token = tokenizer.decode(token_id, skip_special_tokens=True)\n", "    print(token, end='', flush=True)\n", "\n", "# 打印换行符\n", "print()\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型总层数: 64\n"]}], "source": ["from transformers import AutoConfig\n", "model_path = \"/mnt/d/hgmodels/QwQ-32B/\"  # 本地模型路径\n", "config = AutoConfig.from_pretrained(model_path)\n", "total_layers = config.num_hidden_layers  # 获取实际层数 (如32/40)\n", "print(f\"模型总层数: {total_layers}\")  # 调试输出确认"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 定义动态分层规则\n", "def create_device_map(total_layers, gpu_layers=20):\n", "    device_map = {\n", "        \"model.embed_tokens\": 0,  # 嵌入层在GPU\n", "        \"model.norm\": \"cpu\",      # 归一化层在CPU\n", "        \"lm_head\": 0              # 输出头在GPU\n", "    }\n", "    \n", "    # 自动分配隐藏层\n", "    for i in range(total_layers):\n", "        device_map[f\"model.layers.{i}\"] = 0 if i < gpu_layers else \"cpu\"\n", "    \n", "    return device_map\n", "\n", "# 示例：前20层在GPU，剩余层在CPU\n", "device_map = create_device_map(total_layers, gpu_layers=20)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'model.embed_tokens': 0,\n", " 'model.norm': 'cpu',\n", " 'lm_head': 0,\n", " 'model.layers.0': 0,\n", " 'model.layers.1': 0,\n", " 'model.layers.2': 0,\n", " 'model.layers.3': 0,\n", " 'model.layers.4': 0,\n", " 'model.layers.5': 0,\n", " 'model.layers.6': 0,\n", " 'model.layers.7': 0,\n", " 'model.layers.8': 0,\n", " 'model.layers.9': 0,\n", " 'model.layers.10': 0,\n", " 'model.layers.11': 0,\n", " 'model.layers.12': 0,\n", " 'model.layers.13': 0,\n", " 'model.layers.14': 0,\n", " 'model.layers.15': 0,\n", " 'model.layers.16': 0,\n", " 'model.layers.17': 0,\n", " 'model.layers.18': 0,\n", " 'model.layers.19': 0,\n", " 'model.layers.20': 'cpu',\n", " 'model.layers.21': 'cpu',\n", " 'model.layers.22': 'cpu',\n", " 'model.layers.23': 'cpu',\n", " 'model.layers.24': 'cpu',\n", " 'model.layers.25': 'cpu',\n", " 'model.layers.26': 'cpu',\n", " 'model.layers.27': 'cpu',\n", " 'model.layers.28': 'cpu',\n", " 'model.layers.29': 'cpu',\n", " 'model.layers.30': 'cpu',\n", " 'model.layers.31': 'cpu',\n", " 'model.layers.32': 'cpu',\n", " 'model.layers.33': 'cpu',\n", " 'model.layers.34': 'cpu',\n", " 'model.layers.35': 'cpu',\n", " 'model.layers.36': 'cpu',\n", " 'model.layers.37': 'cpu',\n", " 'model.layers.38': 'cpu',\n", " 'model.layers.39': 'cpu',\n", " 'model.layers.40': 'cpu',\n", " 'model.layers.41': 'cpu',\n", " 'model.layers.42': 'cpu',\n", " 'model.layers.43': 'cpu',\n", " 'model.layers.44': 'cpu',\n", " 'model.layers.45': 'cpu',\n", " 'model.layers.46': 'cpu',\n", " 'model.layers.47': 'cpu',\n", " 'model.layers.48': 'cpu',\n", " 'model.layers.49': 'cpu',\n", " 'model.layers.50': 'cpu',\n", " 'model.layers.51': 'cpu',\n", " 'model.layers.52': 'cpu',\n", " 'model.layers.53': 'cpu',\n", " 'model.layers.54': 'cpu',\n", " 'model.layers.55': 'cpu',\n", " 'model.layers.56': 'cpu',\n", " 'model.layers.57': 'cpu',\n", " 'model.layers.58': 'cpu',\n", " 'model.layers.59': 'cpu',\n", " 'model.layers.60': 'cpu',\n", " 'model.layers.61': 'cpu',\n", " 'model.layers.62': 'cpu',\n", " 'model.layers.63': 'cpu'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["device_map"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cuda\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "10b849c2373940f09b66bbc00d21c77b", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/14 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "from transformers import TextStreamer\n", "from transformers import BitsAndBytesConfig\n", "\n", "\n", "# 创建 4bit 量化配置\n", "bnb_config = BitsAndBytesConfig(\n", "    load_in_4bit=True,\n", "    bnb_4bit_quant_type=\"nf4\",\n", "    bnb_4bit_use_double_quant=True,  # 启用双量化节省额外显存\n", "    bnb_4bit_compute_dtype=torch.float16,  # 使用float16加速计算,\n", "    llm_int8_enable_fp32_cpu_offload=True  # 启用CPU卸载\n", ")\n", "\n", "bnb_config = BitsAndBytesConfig(\n", "    load_in_4bit=True,  # 主体使用4-bit量化\n", "    bnb_4bit_quant_type=\"nf4\",\n", "    bnb_4bit_compute_dtype=torch.float16,\n", "    llm_int8_enable_fp32_cpu_offload=True,  # 对无法量化的模块启用CPU卸载\n", "    bnb_4bit_use_double_quant=True,  # 启用双量化\n", ")\n", "\n", "\n", "# 配置参数\n", "model_path = \"/mnt/d/hgmodels/QwQ-32B/\"  # 本地模型路径\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"  # 使用GPU如果可用\n", "print(device)\n", "\n", "# 加载模型和分词器\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    model_path,\n", "    quantization_config=bnb_config,\n", "    device_map=\"auto\",\n", "    offload_folder=\"./offload_cache\",  # 磁盘卸载目录\n", "    max_memory={0: \"15GiB\", \"cpu\": \"24GiB\"},  # 设置GPU和CPU的最大显存\n", "    offload_state_dict=True          # 允许状态字典卸载\n", ")\n", "tokenizer = AutoTokenizer.from_pretrained(model_path)\n", "if tokenizer.pad_token is None:\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "\n", "print(model.quantization_method)  # 应输出 \"bitsandbytes-nf4\"\n", "print(torch.cuda.memory_allocated() / 1024**3, \"GB used\")\n", "\n", "# 创建流式处理器\n", "streamer = TextStreamer(tokenizer)\n", "\n", "# 交互式推理循环\n", "while True:\n", "    # 获取用户输入\n", "    prompt = input(\"\\n输入你的问题 (输入'exit'退出): \")\n", "    if prompt.lower() == 'exit':\n", "        break\n", "        \n", "    # 编码输入\n", "    input_ids = tokenizer(\n", "        prompt, \n", "        return_tensors=\"pt\"\n", "    ).input_ids.to(device)\n", "    \n", "    # 生成响应（流式）\n", "    print(\"\\n回答: \", end=\"\", flush=True)\n", "    generated_ids = model.generate(\n", "        input_ids,\n", "        max_new_tokens=512,\n", "        streamer=streamer,\n", "        pad_token_id=tokenizer.eos_token_id,\n", "        temperature=0.7,\n", "        do_sample=True\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import shutil\n", "\n", "# 定义起始值、步长和结束值\n", "start = 100\n", "step = 100\n", "end = 1600\n", "\n", "# 遍历目录并删除\n", "for i in range(start, end + 1, step):  # 包含结束值，所以用 end + 1\n", "    directory = f\"output/Qwen-0.5B-GRPO/checkpoint-{i}\"  # 生成目录名称\n", "    try:\n", "        if os.path.exists(directory) and os.path.isdir(directory):\n", "            print(f\"Deleting directory: {directory}\")\n", "            shutil.rmtree(directory)  # 删除目录及其内容\n", "            print(f\"Deleted: {directory}\")\n", "        else:\n", "            print(f\"Directory not found: {directory}\")\n", "    except Exception as e:\n", "        print(f\"Failed to delete {directory}. Error: {e}\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 05-10 23:14:30 __init__.py:207] Automatically detected platform cuda.\n", "INFO 05-10 23:14:35 config.py:549] This model supports multiple tasks: {'score', 'generate', 'embed', 'reward', 'classify'}. Defaulting to 'generate'.\n", "INFO 05-10 23:14:35 config.py:1555] Chunked prefill is enabled with max_num_batched_tokens=2048.\n", "INFO 05-10 23:14:35 llm_engine.py:234] Initializing a V0 LLM engine (v0.7.3) with config: model='/mnt/d/hgmodels/Qwen3-30B-A3B/', speculative_config=None, tokenizer='/mnt/d/hgmodels/Qwen3-30B-A3B/', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=40960, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='xgrammar'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=/mnt/d/hgmodels/Qwen3-30B-A3B/, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=False, chunked_prefill_enabled=True, use_async_output_proc=True, disable_mm_preprocessor_cache=False, mm_processor_kwargs=None, pooler_config=None, compilation_config={\"splitting_ops\":[],\"compile_sizes\":[],\"cudagraph_capture_sizes\":[256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],\"max_capture_size\":256}, use_cached_outputs=False, \n", "WARNING 05-10 23:14:36 interface.py:304] Using 'pin_memory=False' as WSL is detected. This may slow down the performance.\n", "INFO 05-10 23:14:36 cuda.py:229] Using Flash Attention backend.\n", "INFO 05-10 23:14:37 model_runner.py:1110] Starting to load model /mnt/d/hgmodels/Qwen3-30B-A3B/...\n", "WARNING 05-10 23:14:37 utils.py:78] Qwen3MoeForCausalLM has no vLLM implementation, falling back to Transformers implementation. Some features may not be supported and performance may not be optimal.\n", "INFO 05-10 23:14:37 transformers.py:129] Using Transformers backend.\n"]}, {"ename": "OutOfMemoryError", "evalue": "CUDA out of memory. Tried to allocate 20.00 MiB. GPU 0 has a total capacity of 15.99 GiB of which 0 bytes is free. Of the allocated memory 27.19 GiB is allocated by PyTorch, and 2.68 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mOutOfMemoryError\u001b[0m                          <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mvllm\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m LLM, SamplingParams\n\u001b[1;32m      2\u001b[0m model_path \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/mnt/d/hgmodels/Qwen3-30B-A3B/\u001b[39m\u001b[38;5;124m\"\u001b[39m  \u001b[38;5;66;03m# 本地模型路径\u001b[39;00m\n\u001b[0;32m----> 4\u001b[0m llm \u001b[38;5;241m=\u001b[39m \u001b[43mLLM\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43menable_chunked_prefill\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# 加载模型\u001b[39;00m\n\u001b[1;32m      6\u001b[0m prompts \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m      7\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mHello, my name is\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m      8\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThe president of the United States is\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     11\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m假设你是一个投资理财专家,现在有150万美金现金,请问如何进行投资规划,请给出详细分析理由\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     12\u001b[0m ]\n\u001b[1;32m     13\u001b[0m \u001b[38;5;66;03m# Create a sampling params object.\u001b[39;00m\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/utils.py:1022\u001b[0m, in \u001b[0;36mdeprecate_args.<locals>.wrapper.<locals>.inner\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m   1015\u001b[0m             msg \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00madditional_message\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1017\u001b[0m         warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m   1018\u001b[0m             \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m(msg),\n\u001b[1;32m   1019\u001b[0m             stacklevel\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m3\u001b[39m,  \u001b[38;5;66;03m# The inner function takes up one level\u001b[39;00m\n\u001b[1;32m   1020\u001b[0m         )\n\u001b[0;32m-> 1022\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/entrypoints/llm.py:242\u001b[0m, in \u001b[0;36mLLM.__init__\u001b[0;34m(self, model, tokenizer, tokenizer_mode, skip_tokenizer_init, trust_remote_code, allowed_local_media_path, tensor_parallel_size, dtype, quantization, revision, tokenizer_revision, seed, gpu_memory_utilization, swap_space, cpu_offload_gb, enforce_eager, max_seq_len_to_capture, disable_custom_all_reduce, disable_async_output_proc, hf_overrides, mm_processor_kwargs, task, override_pooler_config, compilation_config, **kwargs)\u001b[0m\n\u001b[1;32m    239\u001b[0m \u001b[38;5;66;03m# Logic to switch between engines is done at runtime instead of import\u001b[39;00m\n\u001b[1;32m    240\u001b[0m \u001b[38;5;66;03m# to avoid import order issues\u001b[39;00m\n\u001b[1;32m    241\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mengine_class \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_engine_class()\n\u001b[0;32m--> 242\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mllm_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine_class\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_engine_args\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    243\u001b[0m \u001b[43m    \u001b[49m\u001b[43mengine_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43musage_context\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mUsageContext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mLLM_CLASS\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    245\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest_counter \u001b[38;5;241m=\u001b[39m Counter()\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/engine/llm_engine.py:489\u001b[0m, in \u001b[0;36mLLMEngine.from_engine_args\u001b[0;34m(cls, engine_args, usage_context, stat_loggers)\u001b[0m\n\u001b[1;32m    487\u001b[0m executor_class \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_get_executor_cls(engine_config)\n\u001b[1;32m    488\u001b[0m \u001b[38;5;66;03m# Create the LLM engine.\u001b[39;00m\n\u001b[0;32m--> 489\u001b[0m engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m    490\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mengine_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    491\u001b[0m \u001b[43m    \u001b[49m\u001b[43mexecutor_class\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mexecutor_class\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    492\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlog_stats\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mengine_args\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdisable_log_stats\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    493\u001b[0m \u001b[43m    \u001b[49m\u001b[43musage_context\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43musage_context\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    494\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstat_loggers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstat_loggers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    495\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    497\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m engine\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/engine/llm_engine.py:273\u001b[0m, in \u001b[0;36mLLMEngine.__init__\u001b[0;34m(self, vllm_config, executor_class, log_stats, usage_context, stat_loggers, input_registry, mm_registry, use_cached_outputs)\u001b[0m\n\u001b[1;32m    269\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_registry \u001b[38;5;241m=\u001b[39m input_registry\n\u001b[1;32m    270\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_processor \u001b[38;5;241m=\u001b[39m input_registry\u001b[38;5;241m.\u001b[39mcreate_input_processor(\n\u001b[1;32m    271\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_config)\n\u001b[0;32m--> 273\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_executor \u001b[38;5;241m=\u001b[39m \u001b[43mexecutor_class\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvllm_config\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    275\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_config\u001b[38;5;241m.\u001b[39mrunner_type \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpooling\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m    276\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initialize_kv_caches()\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/executor/executor_base.py:52\u001b[0m, in \u001b[0;36mExecutorBase.__init__\u001b[0;34m(self, vllm_config)\u001b[0m\n\u001b[1;32m     50\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprompt_adapter_config \u001b[38;5;241m=\u001b[39m vllm_config\u001b[38;5;241m.\u001b[39mprompt_adapter_config\n\u001b[1;32m     51\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobservability_config \u001b[38;5;241m=\u001b[39m vllm_config\u001b[38;5;241m.\u001b[39mobservability_config\n\u001b[0;32m---> 52\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_init_executor\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     53\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mis_sleeping \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py:47\u001b[0m, in \u001b[0;36mUniProcExecutor._init_executor\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     45\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcollective_rpc(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minit_worker\u001b[39m\u001b[38;5;124m\"\u001b[39m, args\u001b[38;5;241m=\u001b[39m([kwargs], ))\n\u001b[1;32m     46\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcollective_rpc(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minit_device\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 47\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcollective_rpc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mload_model\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py:56\u001b[0m, in \u001b[0;36mUniProcExecutor.collective_rpc\u001b[0;34m(self, method, timeout, args, kwargs)\u001b[0m\n\u001b[1;32m     54\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m kwargs \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m     55\u001b[0m     kwargs \u001b[38;5;241m=\u001b[39m {}\n\u001b[0;32m---> 56\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[43mrun_method\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdriver_worker\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     57\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m [answer]\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/utils.py:2196\u001b[0m, in \u001b[0;36mrun_method\u001b[0;34m(obj, method, args, kwargs)\u001b[0m\n\u001b[1;32m   2194\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   2195\u001b[0m     func \u001b[38;5;241m=\u001b[39m partial(method, obj)  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[0;32m-> 2196\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/worker/worker.py:183\u001b[0m, in \u001b[0;36mWorker.load_model\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    181\u001b[0m     context \u001b[38;5;241m=\u001b[39m nullcontext()\n\u001b[1;32m    182\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m context:\n\u001b[0;32m--> 183\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel_runner\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload_model\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/worker/model_runner.py:1112\u001b[0m, in \u001b[0;36mGPUModelRunnerBase.load_model\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1110\u001b[0m logger\u001b[38;5;241m.\u001b[39minfo(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStarting to load model \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m...\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_config\u001b[38;5;241m.\u001b[39mmodel)\n\u001b[1;32m   1111\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m Devi<PERSON>M<PERSON>oryProfiler(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdevice) \u001b[38;5;28;01mas\u001b[39;00m m:\n\u001b[0;32m-> 1112\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel \u001b[38;5;241m=\u001b[39m \u001b[43mget_model\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvllm_config\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1114\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_memory_usage \u001b[38;5;241m=\u001b[39m m\u001b[38;5;241m.\u001b[39mconsumed_memory\n\u001b[1;32m   1115\u001b[0m logger\u001b[38;5;241m.\u001b[39minfo(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLoading model weights took \u001b[39m\u001b[38;5;132;01m%.4f\u001b[39;00m\u001b[38;5;124m GB\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   1116\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_memory_usage \u001b[38;5;241m/\u001b[39m \u001b[38;5;28mfloat\u001b[39m(\u001b[38;5;241m2\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m30\u001b[39m))\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/model_executor/model_loader/__init__.py:14\u001b[0m, in \u001b[0;36mget_model\u001b[0;34m(vllm_config)\u001b[0m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mget_model\u001b[39m(\u001b[38;5;241m*\u001b[39m, vllm_config: VllmConfig) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m nn\u001b[38;5;241m.\u001b[39mModule:\n\u001b[1;32m     13\u001b[0m     loader \u001b[38;5;241m=\u001b[39m get_model_loader(vllm_config\u001b[38;5;241m.\u001b[39mload_config)\n\u001b[0;32m---> 14\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mloader\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload_model\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvllm_config\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py:406\u001b[0m, in \u001b[0;36mDefaultModelLoader.load_model\u001b[0;34m(self, vllm_config)\u001b[0m\n\u001b[1;32m    404\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m set_default_torch_dtype(model_config\u001b[38;5;241m.\u001b[39mdtype):\n\u001b[1;32m    405\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m target_device:\n\u001b[0;32m--> 406\u001b[0m         model \u001b[38;5;241m=\u001b[39m \u001b[43m_initialize_model\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvllm_config\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    408\u001b[0m     weights_to_load \u001b[38;5;241m=\u001b[39m {name \u001b[38;5;28;01mfor\u001b[39;00m name, _ \u001b[38;5;129;01min\u001b[39;00m model\u001b[38;5;241m.\u001b[39mnamed_parameters()}\n\u001b[1;32m    409\u001b[0m     loaded_weights \u001b[38;5;241m=\u001b[39m model\u001b[38;5;241m.\u001b[39mload_weights(\n\u001b[1;32m    410\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_all_weights(model_config, model))\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py:125\u001b[0m, in \u001b[0;36m_initialize_model\u001b[0;34m(vllm_config, prefix)\u001b[0m\n\u001b[1;32m    122\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvllm_config\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m all_params \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprefix\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m all_params:\n\u001b[1;32m    123\u001b[0m     \u001b[38;5;66;03m# new-style model class\u001b[39;00m\n\u001b[1;32m    124\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m set_current_vllm_config(vllm_config, check_compile\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[0;32m--> 125\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mmodel_class\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvllm_config\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprefix\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    127\u001b[0m msg \u001b[38;5;241m=\u001b[39m (\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvLLM model class should accept `vllm_config` and `prefix` as \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    128\u001b[0m        \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minput arguments. Possibly you have an old-style model class\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    129\u001b[0m        \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m registered from out of tree and it is used for new vLLM version. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    130\u001b[0m        \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCheck https://docs.vllm.ai/en/latest/design/arch_overview.html \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    131\u001b[0m        \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfor the design and update the model class accordingly.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    132\u001b[0m warnings\u001b[38;5;241m.\u001b[39mwarn(msg, \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m, stacklevel\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m)\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/vllm/model_executor/models/transformers.py:138\u001b[0m, in \u001b[0;36mTransformersModel.__init__\u001b[0;34m(self, vllm_config, prefix)\u001b[0m\n\u001b[1;32m    135\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mvocab_size \u001b[38;5;241m=\u001b[39m config\u001b[38;5;241m.\u001b[39mvocab_size\n\u001b[1;32m    136\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39munpadded_vocab_size \u001b[38;5;241m=\u001b[39m config\u001b[38;5;241m.\u001b[39mvocab_size\n\u001b[0;32m--> 138\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel: PreTrainedModel \u001b[38;5;241m=\u001b[39m \u001b[43mAutoModel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_config\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    139\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    140\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattn_implementation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mvllm\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    141\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtorch_dtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvllm_config\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel_config\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    142\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvllm_config\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel_config\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    143\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    144\u001b[0m prefix \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel\u001b[38;5;241m.\u001b[39mbase_model_prefix\n\u001b[1;32m    146\u001b[0m \u001b[38;5;66;03m# MLP modifications\u001b[39;00m\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/transformers/models/auto/auto_factory.py:440\u001b[0m, in \u001b[0;36m_BaseAutoModelClass.from_config\u001b[0;34m(cls, config, **kwargs)\u001b[0m\n\u001b[1;32m    438\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mtype\u001b[39m(config) \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_model_mapping\u001b[38;5;241m.\u001b[39mkeys():\n\u001b[1;32m    439\u001b[0m     model_class \u001b[38;5;241m=\u001b[39m _get_model_class(config, \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_model_mapping)\n\u001b[0;32m--> 440\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mmodel_class\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_from_config\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    442\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    443\u001b[0m     \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnrecognized configuration class \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mconfig\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m for this kind of AutoModel: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    444\u001b[0m     \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mModel type should be one of \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(c\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mfor\u001b[39;00m\u001b[38;5;250m \u001b[39mc\u001b[38;5;250m \u001b[39m\u001b[38;5;129;01min\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_model_mapping\u001b[38;5;241m.\u001b[39mkeys())\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    445\u001b[0m )\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/transformers/modeling_utils.py:279\u001b[0m, in \u001b[0;36mrestore_default_torch_dtype.<locals>._wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    277\u001b[0m old_dtype \u001b[38;5;241m=\u001b[39m torch\u001b[38;5;241m.\u001b[39mget_default_dtype()\n\u001b[1;32m    278\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 279\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    280\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m    281\u001b[0m     torch\u001b[38;5;241m.\u001b[39mset_default_dtype(old_dtype)\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/transformers/modeling_utils.py:2032\u001b[0m, in \u001b[0;36mPreTrainedModel._from_config\u001b[0;34m(cls, config, **kwargs)\u001b[0m\n\u001b[1;32m   2029\u001b[0m         model \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mcls\u001b[39m(config, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m   2031\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 2032\u001b[0m     model \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2034\u001b[0m \u001b[38;5;66;03m# restore default dtype if it was modified\u001b[39;00m\n\u001b[1;32m   2035\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m dtype_orig \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/transformers/models/qwen3_moe/modeling_qwen3_moe.py:577\u001b[0m, in \u001b[0;36mQwen3MoeModel.__init__\u001b[0;34m(self, config)\u001b[0m\n\u001b[1;32m    573\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mvocab_size \u001b[38;5;241m=\u001b[39m config\u001b[38;5;241m.\u001b[39mvocab_size\n\u001b[1;32m    575\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39membed_tokens \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39mEmbedding(config\u001b[38;5;241m.\u001b[39mvocab_size, config\u001b[38;5;241m.\u001b[39mhidden_size, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpadding_idx)\n\u001b[1;32m    576\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlayers \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39mModuleList(\n\u001b[0;32m--> 577\u001b[0m     [\u001b[43mQwen3MoeDecoderLayer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlayer_idx\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m layer_idx \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(config\u001b[38;5;241m.\u001b[39mnum_hidden_layers)]\n\u001b[1;32m    578\u001b[0m )\n\u001b[1;32m    579\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnorm \u001b[38;5;241m=\u001b[39m Qwen3MoeRMSNorm(config\u001b[38;5;241m.\u001b[39mhidden_size, eps\u001b[38;5;241m=\u001b[39mconfig\u001b[38;5;241m.\u001b[39mrms_norm_eps)\n\u001b[1;32m    580\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrotary_emb \u001b[38;5;241m=\u001b[39m Qwen3MoeRotaryEmbedding(config\u001b[38;5;241m=\u001b[39mconfig)\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/transformers/models/qwen3_moe/modeling_qwen3_moe.py:325\u001b[0m, in \u001b[0;36mQwen3MoeDecoderLayer.__init__\u001b[0;34m(self, config, layer_idx)\u001b[0m\n\u001b[1;32m    320\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mself_attn \u001b[38;5;241m=\u001b[39m Qwen3MoeAttention(config, layer_idx)\n\u001b[1;32m    322\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (layer_idx \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m config\u001b[38;5;241m.\u001b[39mmlp_only_layers) \u001b[38;5;129;01mand\u001b[39;00m (\n\u001b[1;32m    323\u001b[0m     config\u001b[38;5;241m.\u001b[39mnum_experts \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m (layer_idx \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m) \u001b[38;5;241m%\u001b[39m config\u001b[38;5;241m.\u001b[39mdecoder_sparse_step \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m    324\u001b[0m ):\n\u001b[0;32m--> 325\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmlp \u001b[38;5;241m=\u001b[39m \u001b[43mQwen3MoeSparseMoeBlock\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    326\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    327\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmlp \u001b[38;5;241m=\u001b[39m Qwen3MoeMLP(config, intermediate_size\u001b[38;5;241m=\u001b[39mconfig\u001b[38;5;241m.\u001b[39mintermediate_size)\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/transformers/models/qwen3_moe/modeling_qwen3_moe.py:249\u001b[0m, in \u001b[0;36mQwen3MoeSparseMoeBlock.__init__\u001b[0;34m(self, config)\u001b[0m\n\u001b[1;32m    246\u001b[0m \u001b[38;5;66;03m# gating\u001b[39;00m\n\u001b[1;32m    247\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgate \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39mLinear(config\u001b[38;5;241m.\u001b[39mhidden_size, config\u001b[38;5;241m.\u001b[39mnum_experts, bias\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[1;32m    248\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexperts \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39mModuleList(\n\u001b[0;32m--> 249\u001b[0m     [\u001b[43mQwen3MoeMLP\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mintermediate_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmoe_intermediate_size\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnum_experts)]\n\u001b[1;32m    250\u001b[0m )\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/transformers/models/qwen3_moe/modeling_qwen3_moe.py:231\u001b[0m, in \u001b[0;36mQwen3MoeMLP.__init__\u001b[0;34m(self, config, intermediate_size)\u001b[0m\n\u001b[1;32m    229\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgate_proj \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39mLinear(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhidden_size, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mintermediate_size, bias\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[1;32m    230\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mup_proj \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39mLinear(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhidden_size, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mintermediate_size, bias\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[0;32m--> 231\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdown_proj \u001b[38;5;241m=\u001b[39m \u001b[43mnn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mLinear\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mintermediate_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhidden_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbias\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    232\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mact_fn \u001b[38;5;241m=\u001b[39m ACT2FN[config\u001b[38;5;241m.\u001b[39mhidden_act]\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/torch/nn/modules/linear.py:106\u001b[0m, in \u001b[0;36mLinear.__init__\u001b[0;34m(self, in_features, out_features, bias, device, dtype)\u001b[0m\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39min_features \u001b[38;5;241m=\u001b[39m in_features\n\u001b[1;32m    104\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mout_features \u001b[38;5;241m=\u001b[39m out_features\n\u001b[1;32m    105\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mweight \u001b[38;5;241m=\u001b[39m Parameter(\n\u001b[0;32m--> 106\u001b[0m     \u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mempty\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43mout_features\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43min_features\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mfactory_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    107\u001b[0m )\n\u001b[1;32m    108\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m bias:\n\u001b[1;32m    109\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbias \u001b[38;5;241m=\u001b[39m Parameter(torch\u001b[38;5;241m.\u001b[39mempty(out_features, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mfactory_kwargs))\n", "File \u001b[0;32m~/workspaces/llf/lib/python3.12/site-packages/torch/utils/_device.py:106\u001b[0m, in \u001b[0;36mDeviceContext.__torch_function__\u001b[0;34m(self, func, types, args, kwargs)\u001b[0m\n\u001b[1;32m    104\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m func \u001b[38;5;129;01min\u001b[39;00m _device_constructors() \u001b[38;5;129;01mand\u001b[39;00m kwargs\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdevice\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    105\u001b[0m     kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdevice\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdevice\n\u001b[0;32m--> 106\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mOutOfMemoryError\u001b[0m: CUDA out of memory. Tried to allocate 20.00 MiB. GPU 0 has a total capacity of 15.99 GiB of which 0 bytes is free. Of the allocated memory 27.19 GiB is allocated by PyTorch, and 2.68 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)"]}], "source": ["from vllm import LLM, SamplingParams\n", "model_path = \"/mnt/d/hgmodels/Qwen3-30B-A3B/\"  # 本地模型路径\n", "\n", "llm = LLM(model=model_path, enable_chunked_prefill=True)  # 加载模型\n", "\n", "prompts = [\n", "    \"Hello, my name is\",\n", "    \"The president of the United States is\",\n", "    \"The capital of France is\",\n", "    \"已知α∈(0,π)，且sinα - √3cosα = 2，求tanα的值，给出详细解题步骤和结果\",\n", "    \"假设你是一个投资理财专家,现在有150万美金现金,请问如何进行投资规划,请给出详细分析理由\",\n", "]\n", "# Create a sampling params object.\n", "sampling_params = SamplingParams(temperature=0.8, top_p=0.95)\n", "\n", "# Generate texts from the prompts. The output is a list of RequestOutput objects\n", "# that contain the prompt, generated text, and other information.\n", "outputs = llm.generate(prompts, sampling_params)\n", "# Print the outputs.\n", "for output in outputs:\n", "    prompt = output.prompt\n", "    generated_text = output.outputs[0].text\n", "    print(f\"Prompt: {prompt!r}, Generated text: {generated_text!r}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vllm serve --model /home/<USER>/model/ --host 0.0.0.0 --port 8000 --dtype auto --max-num-seqs 32 --max-model-len 4096 --tensor-parallel-size 1 --trust-remote-code"]}], "metadata": {"kernelspec": {"display_name": "llf", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}