from agentscope.agents import DialogAgent
from agentscope.message import Msg
from agentscope.pipelines import sequential_pipeline
from agentscope import msghub
import agentscope
import dashscope

dashscope.api_key = "sk-b697b8849a73436bacc4dbeffeac5b78"

# 加载模型配置
agentscope.init(
    model_configs=[
        {
            "config_name": "my_config",
            "model_type": "dashscope_chat",
            "model_name": "qwen-max",
        }
    ]
)

# 创建三个智能体
friday = DialogAgent(
    name="Friday",
    model_config_name="my_config",
    sys_prompt="你是一个名为Friday的助手"
)

saturday = DialogAgent(
    name="Saturday",
    model_config_name="my_config",
    sys_prompt="你是一个名为Saturday的助手"
)

sunday = DialogAgent(
    name="Sunday",
    model_config_name="my_config",
    sys_prompt="你是一个名为Sunday的助手"
)

# 通过msghub创建一个聊天室，智能体的消息会广播给所有参与者
with msghub(
    participants=[friday, saturday, sunday],
    announcement=Msg("user", "玩一个成语接龙的游戏，要求每次只输出一个成语，头一个字与上一个成语尾部的字相同，读音相同也可以，从 杯弓蛇影 开始", "user"),  # 一个问候消息
) as hub:
    # 按顺序发言
    sequential_pipeline([friday, saturday, sunday], x=None)