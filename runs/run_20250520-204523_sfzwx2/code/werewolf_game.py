import random
import agentscope
from typing import List, Dict, Optional, Any
from agentscope.agents import AgentBase
from agentscope.message import Msg

agentscope.init(
    model_configs={
        "config_name": "wolfgame",
        "model_type": "dashscope_chat",
        "model_name": "qwen-max",
        "api_key": "sk-b697b8849a73436bacc4dbeffeac5b78",
    }
)
# =================== 定义各个角色的 Agent ===================

class VillagerAgent(AgentBase):
    def __init__(self, name: str):
        super().__init__(name=name, use_memory=True)

    def speak(self, content: str) -> None:
        msg = self(Msg(name="villager_speak", content=content, role="user"))
        print(f"{self.name} 说: {msg.text}")


class WerewolfAgent(AgentBase):
    def __init__(self, name: str):
        super().__init__(name=name, use_memory=True)

    def kill(self, targets: List[str]) -> str:
        msg = self(Msg(name="wolf_kill", content=f"请选择要杀的人: {', '.join(targets)}", role="user"))
        return msg.text


class SeerAgent(AgentBase):
    def __init__(self, name: str):
        super().__init__(name=name, use_memory=True)

    def check(self, targets: List[str]) -> str:
        msg = self(Msg(name="seer_check", content=f"请选择要查验的人: {', '.join(targets)}", role="user"))
        return msg.text

    def reveal(self, result: str) -> None:
        self(Msg(name="seer_reveal", content=result, role="assistant"))


class WitchAgent(AgentBase):
    def __init__(self, name: str):
        super().__init__(name=name, use_memory=True)
        self.antidote_used = False
        self.poison_used = False

    def use_antidote(self, target: str) -> bool:
        if self.antidote_used:
            return False
        choice = self(Msg(name="witch_antidote", content=f"昨晚被杀的是 {target}，你要救他吗？(是/否)", role="user")).text
        if choice == "是":
            self.antidote_used = True
            return True
        return False

    def use_poison(self, targets: List[str]) -> Optional[str]:
        if self.poison_used:
            return None
        choice = self(Msg(name="witch_poison", content="你想用毒药吗？(是/否)", role="user")).text
        if choice != "是":
            return None
        poison_target = self(Msg(name="witch_poison_target", content=f"请选择毒杀对象: {', '.join(targets)}", role="user")).text
        self.poison_used = True
        return poison_target


# =================== 主控类 - 狼人杀游戏 ===================

class WerewolfGame:
    def __init__(self, player_names: List[str]):
        self.players: List[AgentBase] = []
        self.alive_players: List[AgentBase] = []
        self.roles: Dict[str, str] = {}
        self.day_count = 0

        # 初始化玩家
        for name in player_names:
            if "Villager" in name:
                self.players.append(VillagerAgent(name))
            elif "Werewolf" in name:
                self.players.append(WerewolfAgent(name))
            elif "Seer" in name:
                self.players.append(SeerAgent(name))
            elif "Witch" in name:
                self.players.append(WitchAgent(name))

        self.assign_roles()
        self.alive_players = self.players[:]

    def assign_roles(self):
        """随机分配角色"""
        players_count = len(self.players)
        roles = ["狼人"] * (len(self.players) // players_count) + ["村民"] * (len(self.players) - players_count) + ["预言家", "女巫"]
        random.shuffle(roles)
        self.roles = {player.name: role for player, role in zip(self.players, roles)}
        print("角色分配完成:")
        for name, role in self.roles.items():
            print(f"{name}: {role}")

    def get_alive_names(self) -> List[str]:
        return [p.name for p in self.alive_players]

    def night_phase(self) -> Dict[str, Any]:
        """夜晚阶段处理"""
        actions = {}

        # 狼人杀人
        wolf_agents = [p for p in self.alive_players if self.roles[p.name] == "狼人"]
        alive_names = self.get_alive_names()
        if wolf_agents:
            wolf_target = wolf_agents[0].kill([n for n in alive_names if self.roles[n] != "狼人"])
            actions["kill"] = wolf_target
            print(f"狼人选中了 {wolf_target}")

        # 预言家查验
        seer = next((p for p in self.alive_players if self.roles[p.name] == "预言家"), None)
        if seer:
            check_target = seer.check([n for n in alive_names if n != seer.name])
            result = self.roles[check_target]
            seer.reveal(f"{check_target} 是 {result}")
            print(f"预言家查验了 {check_target}，得知他是 {result}")

        # 女巫救人/毒人
        witch = next((p for p in self.alive_players if self.roles[p.name] == "女巫"), None)
        if witch:
            if "kill" in actions:
                if witch.use_antidote(actions["kill"]):
                    print(f"女巫救了 {actions['kill']}")
                    actions["antidote"] = actions.pop("kill")
            poison_target = witch.use_poison([n for n in alive_names if n != witch.name])
            if poison_target:
                actions["poison"] = poison_target
                print(f"女巫毒杀了 {poison_target}")

        return actions

    def day_phase(self, killed_name: Optional[str], poisoned_name: Optional[str]) -> None:
        """白天阶段讨论和投票"""
        print("\n=== 白天开始 ===")
        if killed_name:
            print(f"昨夜被杀的是: {killed_name}")
        if poisoned_name:
            print(f"昨夜被毒杀的是: {poisoned_name}")

        votes = {}
        alive_names = self.get_alive_names()

        for player in self.alive_players:
            vote_for = player(Msg(
                name="vote",
                content=f"请投票处决一人（可选范围: {', '.join(alive_names)}）",
                role="user"
            )).text
            votes[vote_for] = votes.get(vote_for, 0) + 1

        # 统计票数
        vote_list = sorted(votes.items(), key=lambda x: x[1], reverse=True)
        max_votes = vote_list[0][1]
        candidates = [v[0] for v in vote_list if v[1] == max_votes]
        executed = random.choice(candidates)
        print(f"\n投票结果：{executed} 被处决")

        # 移除被处决者
        for p in self.alive_players:
            if p.name == executed:
                self.alive_players.remove(p)
                break

    def check_winner(self) -> Optional[str]:
        """判断胜负"""
        wolves = sum(1 for p in self.alive_players if self.roles[p.name] == "狼人")
        villagers = len(self.alive_players) - wolves
        if wolves >= villagers:
            return "狼人"
        elif wolves == 0:
            return "村民"
        return None

    def play(self):
        """开始游戏"""
        while True:
            self.day_count += 1
            print(f"\n===== 第 {self.day_count} 天 =====")

            # 夜晚行动
            night_actions = self.night_phase()
            kill_target = night_actions.get("kill")
            poison_target = night_actions.get("poison")
            antidote_target = night_actions.get("antidote")

            # 如果没有被解药救，则死亡
            if kill_target and kill_target not in antidote_target:
                for p in self.alive_players:
                    if p.name == kill_target:
                        self.alive_players.remove(p)
                        print(f"{kill_target} 被杀")
            if poison_target:
                for p in self.alive_players:
                    if p.name == poison_target:
                        self.alive_players.remove(p)
                        print(f"{poison_target} 被毒死")

            # 检查是否有胜利条件达成
            winner = self.check_winner()
            if winner:
                print(f"\n🎉 {winner} 获胜！")
                break

            # 白天阶段
            self.day_phase(kill_target, poison_target)

            winner = self.check_winner()
            if winner:
                print(f"\n🎉 {winner} 获胜！")
                break


# =================== 测试运行 ===================

if __name__ == "__main__":
    # 示例：5个玩家（2狼人，2村民，1预言家）
    names = [
        "Villager1",
        "Villager2",
        "Werewolf1",
        "Werewolf2",
        "Seer1",
        # "Witch1"  # 可以加入女巫
    ]
    game = WerewolfGame(names)
    game.play()