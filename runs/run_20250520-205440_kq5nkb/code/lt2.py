from datasets import load_dataset

# 定义文件路径（注意Windows路径需双反斜杠）
base_path = "/mnt/d/hgmodels/datasets/OpenThoughts-114k/"

# 加载 main 数据集
dataset_main = load_dataset(base_path)
ds = dataset_main['train']
#print(ds[0])

json_data = []
for count,item in enumerate(ds):
    if count > 10000:
        break
        #print(f"data count {count}")
    tmp = {}
    system = item['system']
    conversations = item['conversations']
    tmp['instruction'] = system
    tmp['input'] = conversations[0]['value']
    tmp['output'] = conversations[1]['value']
    json_data.append(tmp)   

#print(json_data[0])

import json

with open('/home/<USER>/workspaces/LLaMA-Factory/data/r1_distilled_data3.json', 'w', encoding='utf-8') as f:
    json.dump(json_data, f, ensure_ascii=False, indent=4)
