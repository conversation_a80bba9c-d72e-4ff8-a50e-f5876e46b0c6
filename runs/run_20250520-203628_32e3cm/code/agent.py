from agentscope.agents import DialogAgent, UserAgent
import agentscope
import dashscope

dashscope.api_key = "sk-b697b8849a73436bacc4dbeffeac5b78"

# 加载模型配置
agentscope.init(
    model_configs=[
        {
            "config_name": "my_config", 
            "model_type": "dashscope_chat",
            "model_name": "qwen-max",
        }
    ]
)

# 创建一个对话智能体和一个用户智能体
dialog_agent = DialogAgent(
    name="Friday",
    model_config_name="my_config",
    sys_prompt="你是一个名为Friday的助手"
)
user_agent = UserAgent(name="user")

# 显式构建工作流程/对话
x = None
while x is None or x.content != "exit":
    x = dialog_agent(x)
    x = user_agent(x)