import gradio as gr
from openai import OpenAI

# 设置 OpenAI 的 API key 和 API base
openai_api_key = "EMPTY"
openai_api_base = "http://localhost:8000/v1"
model_path = "/mnt/d/hgmodels/Qwen2.5-1.5B-Instruct/"  # 本地模型路径

# 创建 OpenAI 客户端
client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)

def predict(message, history):
    # 将聊天历史转换为 OpenAI 格式
    history_openai_format = [{"role": "system", "content": "You are a helpful assistant."}]
    for human, assistant in history:
        history_openai_format.append({"role": "user", "content": human})
        history_openai_format.append({"role": "assistant", "content": assistant})
    history_openai_format.append({"role": "user", "content": message})

    # 创建一个聊天完成请求并发送到 API 服务器
    stream = client.chat.completions.create(
        model="qwen2",
        messages=history_openai_format,
        temperature=0.8,
        stream=True,
    )

    # 从回复流中读取并返回生成的文本
    partial_message = ""
    for chunk in stream:
        partial_message += (chunk.choices[0].delta.content or "")
        yield partial_message

# 创建并启动一个聊天界面
gr.ChatInterface(predict).queue().launch(share=True)