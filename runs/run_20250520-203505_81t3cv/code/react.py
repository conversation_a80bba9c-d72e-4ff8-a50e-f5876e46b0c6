from agentscope.agents import ReActAgentV2, UserAgent
from agentscope.service import ServiceToolkit, execute_python_code
import agentscope


agentscope.init(
    model_configs={
        "config_name": "my_config",
        "model_type": "dashscope_chat",
        "model_name": "qwen-max",
        "api_key": "sk-b697b8849a73436bacc4dbeffeac5b78",
    }
)

# 添加内置工具
toolkit = ServiceToolkit()
toolkit.add(execute_python_code)

GAODE_KEY = "fbc35029bc4fecc003eaa4ec63a9b62d"

# 连接到高德 MCP Server
toolkit.add_mcp_servers(
    {
        "mcpServers": {
            "amap-amap-sse": {
            "url": f"https://mcp.amap.com/sse?key={GAODE_KEY}"
            }
        }
    }
)

#import json
#print(json.dumps(toolkit.json_schemas, indent=4))

#print("Available tools:", toolkit.get("amap.place_search"))




# 创建一个 ReAct 智能体
agent = ReActAgentV2(
    name="Friday",
    model_config_name="my_config",
    service_toolkit=toolkit,
    sys_prompt="你是一个名为Friday的AI助手。"
)
user_agent = UserAgent(name="user")

# 显式构建工作流程/对话
x = None
while x is None or x.content != "exit":
    x = agent(x)
    x = user_agent(x)

