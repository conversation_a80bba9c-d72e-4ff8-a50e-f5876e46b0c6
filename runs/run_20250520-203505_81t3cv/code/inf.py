import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from transformers import TextStreamer
from transformers import BitsAndBytesConfig

from transformers import AutoConfig


model_path = "/mnt/d/hgmodels/QwQ-32B/"  # 本地模型路径
config = AutoConfig.from_pretrained(model_path)
total_layers = config.num_hidden_layers  # 获取实际层数 (如32/40)

# 配置参数
#model_path = "/mnt/d/hgmodels/QwQ-32B/"  # 本地模型路径
device = "cuda" if torch.cuda.is_available() else "cpu"  # 使用GPU如果可用
#print(device)

def create_device_map(total_layers, gpu_layers=40):
    device_map = {
        "model.embed_tokens": 0,  # 嵌入层在GPU
        "model.norm": "cpu",      # 归一化层在CPU
        "lm_head": 0              # 输出头在GPU
    }
    
    # 自动分配隐藏层
    for i in range(total_layers):
        device_map[f"model.layers.{i}"] = 0 if i < gpu_layers else "cpu"
    
    return device_map

# 示例：前20层在GPU，剩余层在CPU
device_map = create_device_map(total_layers, gpu_layers=40)

# 创建 4bit 量化配置
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_quant_type="nf4",
    bnb_4bit_use_double_quant=True,  # 启用双量化节省额外显存
    bnb_4bit_compute_dtype=torch.float16,  # 使用float16加速计算,
    llm_int8_enable_fp32_cpu_offload=True  # 启用CPU卸载
)

bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,  # 主体使用4-bit量化
    bnb_4bit_quant_type="nf4",
    bnb_4bit_compute_dtype=torch.float16,
    llm_int8_enable_fp32_cpu_offload=True,  # 对无法量化的模块启用CPU卸载
    bnb_4bit_use_double_quant=True,  # 启用双量化
)



# 加载模型和分词器
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    quantization_config=bnb_config,
    #attn_implementation='flash_attention_2',
    device_map=device_map,
    offload_folder="./offload_cache",  # 磁盘卸载目录
    max_memory={0: "15GiB", "cpu": "24GiB"},  # 设置GPU和CPU的最大显存
    offload_state_dict=True          # 允许状态字典卸载
)
tokenizer = AutoTokenizer.from_pretrained(model_path)
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

print(model.quantization_method)  # 应输出 "bitsandbytes-nf4"
print(torch.cuda.memory_allocated() / 1024**3, "GB used")

# 创建流式处理器
streamer = TextStreamer(tokenizer)

# 交互式推理循环
while True:
    # 获取用户输入
    prompt = input("\n输入你的问题 (输入'exit'退出): ")
    if prompt.lower() == 'exit':
        break
        
    # 编码输入
    input_ids = tokenizer(
        prompt, 
        return_tensors="pt"
    ).input_ids.to(device)
    
    # 生成响应（流式）
    print("\n回答: ", end="", flush=True)
    generated_ids = model.generate(
        input_ids,
        max_new_tokens=512,
        streamer=streamer,
        #pad_token_id=tokenizer.eos_token_id,
        temperature=0.7,
        do_sample=True
    )