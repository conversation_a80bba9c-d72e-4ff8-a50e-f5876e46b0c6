import torch
import flash_attn
from flash_attn import flash_attn_func

print(f"Flash Attention version: {flash_attn.__version__}")

if torch.cuda.is_available():
    q = torch.randn(2, 8, 32, 64, device='cuda')
    k = torch.randn(2, 8, 32, 64, device='cuda')
    v = torch.randn(2, 8, 32, 64, device='cuda')
    q = q.to(torch.bfloat16)
    k = k.to(torch.bfloat16)
    v = v.to(torch.bfloat16)

    output = flash_attn_func(q, k, v)
    print("Flash Attention test successful!")
else:
    print("CUDA device not available!")
