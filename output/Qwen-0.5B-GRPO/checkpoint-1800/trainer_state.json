{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.9633395772009633, "eval_steps": 500, "global_step": 1800, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"completion_length": 402.675, "epoch": 0.005351886540005352, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 2.6737967914438503e-07, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 10}, {"completion_length": 396.625, "epoch": 0.010703773080010704, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 5.347593582887701e-07, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 20}, {"completion_length": 467.9625, "epoch": 0.016055659620016056, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 8.021390374331551e-07, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 30}, {"completion_length": 369.8375, "epoch": 0.02140754616002141, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 1.0695187165775401e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 40}, {"completion_length": 351.55, "epoch": 0.02675943270002676, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 1.3368983957219254e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 50}, {"completion_length": 360.0375, "epoch": 0.03211131924003211, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 1.6042780748663103e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 60}, {"completion_length": 337.15, "epoch": 0.037463205780037465, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 1.8716577540106954e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 70}, {"completion_length": 322.9375, "epoch": 0.04281509232004282, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 2.1390374331550802e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 80}, {"completion_length": 379.2125, "epoch": 0.048166978860048164, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 2.4064171122994653e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 90}, {"completion_length": 377.825, "epoch": 0.05351886540005352, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 2.673796791443851e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 100}, {"completion_length": 413.9875, "epoch": 0.05887075194005887, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 2.9411764705882355e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 110}, {"completion_length": 377.6375, "epoch": 0.06422263848006422, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 3.2085561497326205e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 120}, {"completion_length": 423.9875, "epoch": 0.06957452502006957, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 3.4759358288770056e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 130}, {"completion_length": 357.8625, "epoch": 0.07492641156007493, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 3.7433155080213907e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 140}, {"completion_length": 316.375, "epoch": 0.08027829810008028, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.010695187165775e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 150}, {"completion_length": 350.525, "epoch": 0.08563018464008564, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.2780748663101604e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 160}, {"completion_length": 427.25, "epoch": 0.09098207118009098, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.5454545454545455e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 170}, {"completion_length": 403.675, "epoch": 0.09633395772009633, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.812834224598931e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 180}, {"completion_length": 419.175, "epoch": 0.10168584426010169, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.9999607069534e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 190}, {"completion_length": 397.1375, "epoch": 0.10703773080010703, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.999262198262866e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 200}, {"completion_length": 382.7625, "epoch": 0.1123896173401124, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.997690791572498e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 210}, {"completion_length": 408.775, "epoch": 0.11774150388011774, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.9952470357153715e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 220}, {"completion_length": 416.4, "epoch": 0.1230933904201231, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.991931784203215e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 230}, {"completion_length": 365.5125, "epoch": 0.12844527696012845, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.987746194928311e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 240}, {"completion_length": 378.2875, "epoch": 0.1337971635001338, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.98269172975909e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 250}, {"completion_length": 381.1125, "epoch": 0.13914905004013914, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.976770154029556e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 260}, {"completion_length": 308.4, "epoch": 0.1445009365801445, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.969983535922712e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 270}, {"completion_length": 355.525, "epoch": 0.14985282312014986, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.962334245748237e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 280}, {"completion_length": 377.4875, "epoch": 0.1552047096601552, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.9538249551146145e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 290}, {"completion_length": 411.8125, "epoch": 0.16055659620016055, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.944458635996045e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 300}, {"completion_length": 462.15, "epoch": 0.1659084827401659, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.934238559694448e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 310}, {"completion_length": 356.525, "epoch": 0.17126036928017127, "grad_norm": 0.0, "kl": 0.0, "learning_rate": 4.923168295696917e-06, "loss": 0.0, "reward": 0.0, "reward_std": 0.0, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.0, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 320}, {"completion_length": 399.0125, "epoch": 0.17661225582017662, "grad_norm": 52.25, "kl": 0.0, "learning_rate": 4.911251710429034e-06, "loss": -0.0, "reward": 0.00625, "reward_std": 0.00883883461356163, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.00625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 330}, {"completion_length": 360.4875, "epoch": 0.18196414236018196, "grad_norm": 26.25, "kl": 0.09994267320726066, "learning_rate": 4.898492965904475e-06, "loss": 0.004, "reward": 0.025, "reward_std": 0.03535533845424652, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.025, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 340}, {"completion_length": 163.8375, "epoch": 0.1873160289001873, "grad_norm": 39.75, "kl": 2.969520677998662, "learning_rate": 4.884896518271371e-06, "loss": 0.1188, "reward": 0.375, "reward_std": 0.07071067690849304, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.325, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 350}, {"completion_length": 38.9875, "epoch": 0.19266791544019266, "grad_norm": 33.25, "kl": 3.5972430408000946, "learning_rate": 4.870467116255947e-06, "loss": 0.1439, "reward": 0.575, "reward_std": 0.19445436149835588, "rewards/correctness_reward_func": 0.15, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 360}, {"completion_length": 94.275, "epoch": 0.19801980198019803, "grad_norm": 40.25, "kl": 3.4824458181858065, "learning_rate": 4.8552097995039696e-06, "loss": 0.1393, "reward": 0.4375, "reward_std": 0.17677669376134872, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.3875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 370}, {"completion_length": 34.65, "epoch": 0.20337168852020338, "grad_norm": 4.875, "kl": 4.013144779205322, "learning_rate": 4.83912989682059e-06, "loss": 0.1605, "reward": 0.4390625, "reward_std": 0.03756504710763693, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0015625, "step": 380}, {"completion_length": 198.6, "epoch": 0.20872357506020872, "grad_norm": 50.0, "kl": 2.349220260977745, "learning_rate": 4.822233024309193e-06, "loss": 0.094, "reward": 0.21377499997615815, "reward_std": 0.12194056287407876, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.21875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": -0.0049750000238418576, "step": 390}, {"completion_length": 76.225, "epoch": 0.21407546160021407, "grad_norm": 14.875, "kl": 3.5551588267087935, "learning_rate": 4.804525083409902e-06, "loss": 0.1422, "reward": 0.475, "reward_std": 0.15909902304410933, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 400}, {"completion_length": 35.0375, "epoch": 0.2194273481402194, "grad_norm": 17.625, "kl": 4.2225911796092985, "learning_rate": 4.786012258838433e-06, "loss": 0.1689, "reward": 0.58125, "reward_std": 0.15026018843054773, "rewards/correctness_reward_func": 0.125, "rewards/int_reward_func": 0.45625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 410}, {"completion_length": 49.0, "epoch": 0.2247792346802248, "grad_norm": 2.765625, "kl": 3.5541919469833374, "learning_rate": 4.7667010164260016e-06, "loss": 0.1422, "reward": 0.46875, "reward_std": 0.11490484848618507, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 420}, {"completion_length": 186.2, "epoch": 0.23013112122023013, "grad_norm": 14.0, "kl": 2.933896649815142, "learning_rate": 4.7465981008610555e-06, "loss": 0.1174, "reward": 0.425, "reward_std": 0.194454362988472, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 430}, {"completion_length": 196.175, "epoch": 0.23548300776023548, "grad_norm": 7.5, "kl": 2.7783277690410615, "learning_rate": 4.725710533333608e-06, "loss": 0.1111, "reward": 0.43125, "reward_std": 0.22097086608409883, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.38125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 440}, {"completion_length": 89.8375, "epoch": 0.24083489430024083, "grad_norm": 2.046875, "kl": 3.923724091053009, "learning_rate": 4.7040456090830015e-06, "loss": 0.1569, "reward": 0.50625, "reward_std": 0.06187184229493141, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.45625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 450}, {"completion_length": 144.45, "epoch": 0.2461867808402462, "grad_norm": 1.640625, "kl": 3.5966563768684865, "learning_rate": 4.681610894849957e-06, "loss": 0.1439, "reward": 0.50625, "reward_std": 0.06187184229493141, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.45625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 460}, {"completion_length": 280.975, "epoch": 0.25153866738025155, "grad_norm": 2.125, "kl": 2.732682373933494, "learning_rate": 4.658414226233792e-06, "loss": 0.1093, "reward": 0.48125, "reward_std": 0.16793785616755486, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.43125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 470}, {"completion_length": 304.85, "epoch": 0.2568905539202569, "grad_norm": 38.25, "kl": 2.2776587128639223, "learning_rate": 4.6344637049557495e-06, "loss": 0.0911, "reward": 0.38125, "reward_std": 0.16793785840272904, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.35625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 480}, {"completion_length": 243.65, "epoch": 0.26224244046026224, "grad_norm": 17.0, "kl": 3.075945180654526, "learning_rate": 4.609767696029365e-06, "loss": 0.123, "reward": 0.4, "reward_std": 0.12374368458986282, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 490}, {"completion_length": 240.6375, "epoch": 0.2675943270002676, "grad_norm": 24.375, "kl": 3.383565852418542, "learning_rate": 4.584334824838885e-06, "loss": 0.1353, "reward": 0.45, "reward_std": 0.22980969846248628, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 500}, {"completion_length": 124.7875, "epoch": 0.27294621354027293, "grad_norm": 9.4375, "kl": 3.985461801290512, "learning_rate": 4.558173974126749e-06, "loss": 0.1594, "reward": 0.55625, "reward_std": 0.13258251994848252, "rewards/correctness_reward_func": 0.1, "rewards/int_reward_func": 0.45625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 510}, {"completion_length": 152.775, "epoch": 0.2782981000802783, "grad_norm": 1.7734375, "kl": 3.7376016905531286, "learning_rate": 4.5312942808911775e-06, "loss": 0.1495, "reward": 0.41875, "reward_std": 0.15026018917560577, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.36875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 520}, {"completion_length": 411.2375, "epoch": 0.2836499866202836, "grad_norm": 36.0, "kl": 2.340210899338126, "learning_rate": 4.503705133194967e-06, "loss": 0.0936, "reward": 0.35625, "reward_std": 0.2740038722753525, "rewards/correctness_reward_func": 0.1, "rewards/int_reward_func": 0.25625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 530}, {"completion_length": 218.55, "epoch": 0.289001873160289, "grad_norm": 16.125, "kl": 3.05252528488636, "learning_rate": 4.475416166886593e-06, "loss": 0.1221, "reward": 0.375, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 540}, {"completion_length": 105.775, "epoch": 0.29435375970029437, "grad_norm": 2.5625, "kl": 4.188370126485824, "learning_rate": 4.446437262234769e-06, "loss": 0.1675, "reward": 0.44375, "reward_std": 0.07954951152205467, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 550}, {"completion_length": 97.15, "epoch": 0.2997056462402997, "grad_norm": 3.890625, "kl": 4.2715287851169705, "learning_rate": 4.416778540477646e-06, "loss": 0.1709, "reward": 0.46875, "reward_std": 0.04419417306780815, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.46875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 560}, {"completion_length": 193.2125, "epoch": 0.30505753278030506, "grad_norm": 6.5625, "kl": 2.859215196967125, "learning_rate": 4.386450360287842e-06, "loss": 0.1144, "reward": 0.4375, "reward_std": 0.2474873684346676, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.3625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 570}, {"completion_length": 228.775, "epoch": 0.3104094193203104, "grad_norm": 32.5, "kl": 2.607811248116195, "learning_rate": 4.355463314154551e-06, "loss": 0.1043, "reward": 0.38125, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.38125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 580}, {"completion_length": 94.2125, "epoch": 0.31576130586031576, "grad_norm": 142.0, "kl": 4.28378837108612, "learning_rate": 4.323828224683983e-06, "loss": 0.1714, "reward": 0.4375, "reward_std": 0.12374368533492089, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 590}, {"completion_length": 147.525, "epoch": 0.3211131924003211, "grad_norm": 22.375, "kl": 3.6188765704631805, "learning_rate": 4.29155614081944e-06, "loss": 0.1448, "reward": 0.44375, "reward_std": 0.22097086608409883, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.36875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 600}, {"completion_length": 165.5875, "epoch": 0.32646507894032645, "grad_norm": 3.015625, "kl": 3.0385137766599657, "learning_rate": 4.258658333982335e-06, "loss": 0.1215, "reward": 0.38125, "reward_std": 0.13258251920342445, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.38125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 610}, {"completion_length": 118.7625, "epoch": 0.3318169654803318, "grad_norm": 29.875, "kl": 3.3866578936576843, "learning_rate": 4.2251462941355075e-06, "loss": 0.1355, "reward": 0.40625, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.40625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 620}, {"completion_length": 146.3375, "epoch": 0.3371688520203372, "grad_norm": 13.5, "kl": 3.5455169856548308, "learning_rate": 4.191031725770216e-06, "loss": 0.1418, "reward": 0.40625, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.40625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 630}, {"completion_length": 81.975, "epoch": 0.34252073856034254, "grad_norm": 6.40625, "kl": 3.774731531739235, "learning_rate": 4.1563265438182e-06, "loss": 0.151, "reward": 0.4125, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 640}, {"completion_length": 107.025, "epoch": 0.3478726251003479, "grad_norm": 12.6875, "kl": 3.798072302341461, "learning_rate": 4.1210428694902444e-06, "loss": 0.1519, "reward": 0.425, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 650}, {"completion_length": 58.45, "epoch": 0.35322451164035323, "grad_norm": 11.25, "kl": 4.174359548091888, "learning_rate": 4.085193026042695e-06, "loss": 0.167, "reward": 0.4625, "reward_std": 0.10606601610779762, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 660}, {"completion_length": 119.325, "epoch": 0.3585763981803586, "grad_norm": 1.390625, "kl": 3.7882723987102507, "learning_rate": 4.048789534473414e-06, "loss": 0.1515, "reward": 0.475, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 670}, {"completion_length": 14.4125, "epoch": 0.3639282847203639, "grad_norm": 2.75, "kl": 4.261242949962616, "learning_rate": 4.011845109148666e-06, "loss": 0.1704, "reward": 0.54375, "reward_std": 0.00883883461356163, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.49375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 680}, {"completion_length": 50.025, "epoch": 0.36928017126036927, "grad_norm": 13.125, "kl": 3.9539060473442076, "learning_rate": 3.974372653362466e-06, "loss": 0.1582, "reward": 0.49375, "reward_std": 0.15026018843054773, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 690}, {"completion_length": 112.725, "epoch": 0.3746320578003746, "grad_norm": 3.984375, "kl": 3.8889672219753266, "learning_rate": 3.936385254829953e-06, "loss": 0.1556, "reward": 0.475, "reward_std": 0.19445436224341392, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 700}, {"completion_length": 126.1875, "epoch": 0.37998394434037996, "grad_norm": 2.75, "kl": 4.0582437992095945, "learning_rate": 3.897896181116341e-06, "loss": 0.1623, "reward": 0.4140625, "reward_std": 0.15688931420445443, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.3875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0015625, "step": 710}, {"completion_length": 131.7, "epoch": 0.3853358308803853, "grad_norm": 13.4375, "kl": 3.91920815333724, "learning_rate": 3.858918875003053e-06, "loss": 0.1568, "reward": 0.3875, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.3875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 720}, {"completion_length": 124.8375, "epoch": 0.3906877174203907, "grad_norm": 15.5, "kl": 3.9181196808815004, "learning_rate": 3.819466949792677e-06, "loss": 0.1567, "reward": 0.46875, "reward_std": 0.22097086608409883, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 730}, {"completion_length": 87.3375, "epoch": 0.39603960396039606, "grad_norm": 18.625, "kl": 3.995502543449402, "learning_rate": 3.779554184554345e-06, "loss": 0.1598, "reward": 0.4625, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 740}, {"completion_length": 75.9875, "epoch": 0.4013914905004014, "grad_norm": 2.234375, "kl": 3.762271761894226, "learning_rate": 3.739194519311221e-06, "loss": 0.1505, "reward": 0.45, "reward_std": 0.12374368533492089, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 750}, {"completion_length": 99.925, "epoch": 0.40674337704040675, "grad_norm": 10.0625, "kl": 3.6391643047332765, "learning_rate": 3.6984020501717864e-06, "loss": 0.1456, "reward": 0.41875, "reward_std": 0.09722718074917794, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 760}, {"completion_length": 98.7875, "epoch": 0.4120952635804121, "grad_norm": 18.0, "kl": 3.8981038689613343, "learning_rate": 3.6571910244065927e-06, "loss": 0.1559, "reward": 0.4375, "reward_std": 0.12374368458986282, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 770}, {"completion_length": 128.6625, "epoch": 0.41744715012041744, "grad_norm": 18.625, "kl": 3.6187477350234984, "learning_rate": 3.6155758354722313e-06, "loss": 0.1447, "reward": 0.4125, "reward_std": 0.1414213538169861, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.3875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 780}, {"completion_length": 157.225, "epoch": 0.4227990366604228, "grad_norm": 1.875, "kl": 3.564037394523621, "learning_rate": 3.573571017984242e-06, "loss": 0.1426, "reward": 0.4375, "reward_std": 0.12374368458986282, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.3875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 790}, {"completion_length": 67.425, "epoch": 0.42815092320042814, "grad_norm": 5.125, "kl": 3.8578239917755126, "learning_rate": 3.5311912426407185e-06, "loss": 0.1543, "reward": 0.4375, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 800}, {"completion_length": 138.5, "epoch": 0.4335028097404335, "grad_norm": 7.46875, "kl": 3.7828989386558534, "learning_rate": 3.4884513110983886e-06, "loss": 0.1513, "reward": 0.3875, "reward_std": 0.15909902304410933, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.3875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 810}, {"completion_length": 35.425, "epoch": 0.4388546962804388, "grad_norm": 1.96875, "kl": 4.94094443321228, "learning_rate": 3.445366150802953e-06, "loss": 0.1976, "reward": 0.46875, "reward_std": 0.097227181494236, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 820}, {"completion_length": 74.9875, "epoch": 0.44420658282044423, "grad_norm": 18.75, "kl": 4.153316825628281, "learning_rate": 3.4019508097754912e-06, "loss": 0.1661, "reward": 0.45, "reward_std": 0.07071067690849304, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.45, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 830}, {"completion_length": 40.825, "epoch": 0.4495584693604496, "grad_norm": 5.4375, "kl": 4.687303352355957, "learning_rate": 3.358220451356758e-06, "loss": 0.1875, "reward": 0.49375, "reward_std": 0.07954951152205467, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 840}, {"completion_length": 23.8, "epoch": 0.4549103559004549, "grad_norm": 1.390625, "kl": 5.114961433410644, "learning_rate": 3.3141903489111966e-06, "loss": 0.2046, "reward": 0.45, "reward_std": 0.03535533845424652, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.45, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 850}, {"completion_length": 85.975, "epoch": 0.46026224244046027, "grad_norm": 24.0, "kl": 4.009407722949982, "learning_rate": 3.269875880492532e-06, "loss": 0.1604, "reward": 0.48021249771118163, "reward_std": 0.1694051094353199, "rewards/correctness_reward_func": 0.125, "rewards/int_reward_func": 0.4125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": -0.05728750228881836, "step": 860}, {"completion_length": 135.7875, "epoch": 0.4656141289804656, "grad_norm": 11.4375, "kl": 3.839717161655426, "learning_rate": 3.2252925234727955e-06, "loss": 0.1536, "reward": 0.44375, "reward_std": 0.09722718074917794, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 870}, {"completion_length": 98.1625, "epoch": 0.47096601552047096, "grad_norm": 10.4375, "kl": 4.018971306085587, "learning_rate": 3.180455849136664e-06, "loss": 0.1608, "reward": 0.41875, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 880}, {"completion_length": 106.1875, "epoch": 0.4763179020604763, "grad_norm": 15.25, "kl": 4.057318341732025, "learning_rate": 3.1353815172429937e-06, "loss": 0.1623, "reward": 0.45625, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.43125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 890}, {"completion_length": 113.3125, "epoch": 0.48166978860048165, "grad_norm": 9.3125, "kl": 3.9747119784355163, "learning_rate": 3.0900852705554618e-06, "loss": 0.159, "reward": 0.425, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 900}, {"completion_length": 89.95, "epoch": 0.487021675140487, "grad_norm": 10.6875, "kl": 4.626534122228622, "learning_rate": 3.044582929344212e-06, "loss": 0.1851, "reward": 0.44375, "reward_std": 0.11490484848618507, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 910}, {"completion_length": 118.625, "epoch": 0.4923735616804924, "grad_norm": 10.9375, "kl": 3.9000374913215636, "learning_rate": 2.9988903858604275e-06, "loss": 0.156, "reward": 0.45, "reward_std": 0.12374368533492089, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 920}, {"completion_length": 174.55, "epoch": 0.49772544822049775, "grad_norm": 22.0, "kl": 3.840768164396286, "learning_rate": 2.9530235987857715e-06, "loss": 0.1536, "reward": 0.3875, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.3875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 930}, {"completion_length": 74.275, "epoch": 0.5030773347605031, "grad_norm": 3.28125, "kl": 4.532252812385559, "learning_rate": 2.9069985876586206e-06, "loss": 0.1813, "reward": 0.525, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.45, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 940}, {"completion_length": 64.3875, "epoch": 0.5084292213005084, "grad_norm": 13.625, "kl": 4.140095508098602, "learning_rate": 2.8608314272790427e-06, "loss": 0.1656, "reward": 0.5125, "reward_std": 0.05303300768136978, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.4625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 950}, {"completion_length": 126.75, "epoch": 0.5137811078405138, "grad_norm": 3.0625, "kl": 4.062346315383911, "learning_rate": 2.8145382420944767e-06, "loss": 0.1625, "reward": 0.4375, "reward_std": 0.1414213538169861, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.3875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 960}, {"completion_length": 62.2, "epoch": 0.5191329943805192, "grad_norm": 1.1015625, "kl": 4.2279501080513, "learning_rate": 2.768135200568073e-06, "loss": 0.1691, "reward": 0.5, "reward_std": 0.12374368533492089, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.45, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 970}, {"completion_length": 90.0125, "epoch": 0.5244848809205245, "grad_norm": 2.96875, "kl": 4.086262410879135, "learning_rate": 2.721638509531656e-06, "loss": 0.1635, "reward": 0.425, "reward_std": 0.07071067690849304, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 980}, {"completion_length": 63.0, "epoch": 0.5298367674605299, "grad_norm": 3.40625, "kl": 4.906267827749252, "learning_rate": 2.6750644085252926e-06, "loss": 0.1963, "reward": 0.51875, "reward_std": 0.07954951152205467, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 990}, {"completion_length": 123.4125, "epoch": 0.5351886540005352, "grad_norm": 3.109375, "kl": 3.958200514316559, "learning_rate": 2.6284291641254308e-06, "loss": 0.1583, "reward": 0.4375, "reward_std": 0.12374368533492089, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1000}, {"completion_length": 106.9, "epoch": 0.5405405405405406, "grad_norm": 6.5625, "kl": 4.325132045149803, "learning_rate": 2.5817490642636e-06, "loss": 0.173, "reward": 0.39375, "reward_std": 0.07954951152205467, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1010}, {"completion_length": 60.55, "epoch": 0.5458924270805459, "grad_norm": 1.984375, "kl": 4.142677652835846, "learning_rate": 2.5350404125376494e-06, "loss": 0.1657, "reward": 0.51875, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1020}, {"completion_length": 75.1875, "epoch": 0.5512443136205513, "grad_norm": 3.796875, "kl": 3.9425957679748533, "learning_rate": 2.4883195225175188e-06, "loss": 0.1577, "reward": 0.425, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1030}, {"completion_length": 119.275, "epoch": 0.5565962001605566, "grad_norm": 1.6171875, "kl": 3.6741875648498534, "learning_rate": 2.441602712047519e-06, "loss": 0.147, "reward": 0.41875, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1040}, {"completion_length": 118.0875, "epoch": 0.561948086700562, "grad_norm": 5.59375, "kl": 3.5556544959545135, "learning_rate": 2.3949062975471325e-06, "loss": 0.1422, "reward": 0.39375, "reward_std": 0.13258251920342445, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1050}, {"completion_length": 98.3875, "epoch": 0.5672999732405672, "grad_norm": 1.390625, "kl": 4.267025935649872, "learning_rate": 2.348246588312296e-06, "loss": 0.1707, "reward": 0.475, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1060}, {"completion_length": 92.7125, "epoch": 0.5726518597805726, "grad_norm": 2.546875, "kl": 3.4863489866256714, "learning_rate": 2.301639880819183e-06, "loss": 0.1395, "reward": 0.425, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1070}, {"completion_length": 80.05, "epoch": 0.578003746320578, "grad_norm": 15.3125, "kl": 3.9666524291038514, "learning_rate": 2.255102453032456e-06, "loss": 0.1587, "reward": 0.41875, "reward_std": 0.07954951152205467, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1080}, {"completion_length": 62.675, "epoch": 0.5833556328605833, "grad_norm": 15.9375, "kl": 4.1067330777645115, "learning_rate": 2.208650558719992e-06, "loss": 0.1643, "reward": 0.5375, "reward_std": 0.1414213553071022, "rewards/correctness_reward_func": 0.1, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1090}, {"completion_length": 74.375, "epoch": 0.5887075194005887, "grad_norm": 20.0, "kl": 4.333908200263977, "learning_rate": 2.162300421776052e-06, "loss": 0.1734, "reward": 0.45, "reward_std": 0.07071067690849304, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.45, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1100}, {"completion_length": 59.6125, "epoch": 0.594059405940594, "grad_norm": 1.8984375, "kl": 3.8602193236351012, "learning_rate": 2.1160682305548867e-06, "loss": 0.1544, "reward": 0.46875, "reward_std": 0.09722718074917794, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1110}, {"completion_length": 71.725, "epoch": 0.5994112924805994, "grad_norm": 1.7265625, "kl": 4.949640464782715, "learning_rate": 2.069970132216754e-06, "loss": 0.198, "reward": 0.46875, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1120}, {"completion_length": 61.8125, "epoch": 0.6047631790206047, "grad_norm": 45.75, "kl": 4.979898166656494, "learning_rate": 2.024022227088329e-06, "loss": 0.1992, "reward": 0.45625, "reward_std": 0.06187184229493141, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.45625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1130}, {"completion_length": 61.7625, "epoch": 0.6101150655606101, "grad_norm": 1.59375, "kl": 4.21127564907074, "learning_rate": 1.9782405630394635e-06, "loss": 0.1685, "reward": 0.475, "reward_std": 0.08838834688067436, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.45, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1140}, {"completion_length": 67.0875, "epoch": 0.6154669521006154, "grad_norm": 16.625, "kl": 4.030303680896759, "learning_rate": 1.9326411298782706e-06, "loss": 0.1612, "reward": 0.45, "reward_std": 0.05303300768136978, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.45, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1150}, {"completion_length": 113.5375, "epoch": 0.6208188386406208, "grad_norm": 6.53125, "kl": 3.716171461343765, "learning_rate": 1.8872398537664902e-06, "loss": 0.1486, "reward": 0.41875, "reward_std": 0.07954951152205467, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1160}, {"completion_length": 128.1, "epoch": 0.6261707251806262, "grad_norm": 1.8046875, "kl": 4.228364098072052, "learning_rate": 1.8420525916570811e-06, "loss": 0.1691, "reward": 0.40625, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.40625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1170}, {"completion_length": 169.7125, "epoch": 0.6315226117206315, "grad_norm": 7.4375, "kl": 3.42938272356987, "learning_rate": 1.797095125755984e-06, "loss": 0.1372, "reward": 0.36875, "reward_std": 0.13258251920342445, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.36875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1180}, {"completion_length": 119.3875, "epoch": 0.6368744982606369, "grad_norm": 22.5, "kl": 3.5559818267822267, "learning_rate": 1.7523831580099938e-06, "loss": 0.1422, "reward": 0.475, "reward_std": 0.1590990237891674, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1190}, {"completion_length": 119.2375, "epoch": 0.6422263848006422, "grad_norm": 21.375, "kl": 3.740132969617844, "learning_rate": 1.7079323046226612e-06, "loss": 0.1496, "reward": 0.4625, "reward_std": 0.12374368458986282, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.4125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1200}, {"completion_length": 64.1, "epoch": 0.6475782713406476, "grad_norm": 18.75, "kl": 4.434485375881195, "learning_rate": 1.6637580906001405e-06, "loss": 0.1774, "reward": 0.4625, "reward_std": 0.05303300768136978, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1210}, {"completion_length": 57.2125, "epoch": 0.6529301578806529, "grad_norm": 41.5, "kl": 4.377779877185821, "learning_rate": 1.6198759443288941e-06, "loss": 0.1751, "reward": 0.4875, "reward_std": 0.1414213575422764, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1220}, {"completion_length": 143.625, "epoch": 0.6582820444206583, "grad_norm": 3.21875, "kl": 3.6045849889516832, "learning_rate": 1.5763011921871377e-06, "loss": 0.1442, "reward": 0.39375, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1230}, {"completion_length": 110.2, "epoch": 0.6636339309606636, "grad_norm": 14.0625, "kl": 3.6676953077316283, "learning_rate": 1.5330490531919132e-06, "loss": 0.1467, "reward": 0.46875, "reward_std": 0.18561552762985228, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1240}, {"completion_length": 84.2, "epoch": 0.668985817500669, "grad_norm": 9.5, "kl": 4.855313992500305, "learning_rate": 1.4901346336836603e-06, "loss": 0.1942, "reward": 0.50625, "reward_std": 0.13258251994848252, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.45625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1250}, {"completion_length": 77.8625, "epoch": 0.6743377040406744, "grad_norm": 16.625, "kl": 3.945159435272217, "learning_rate": 1.4475729220501439e-06, "loss": 0.1578, "reward": 0.46875, "reward_std": 0.07954951152205467, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1260}, {"completion_length": 88.0625, "epoch": 0.6796895905806797, "grad_norm": 11.875, "kl": 4.0481435537338255, "learning_rate": 1.4053787834915753e-06, "loss": 0.1619, "reward": 0.45625, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.43125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1270}, {"completion_length": 106.5125, "epoch": 0.6850414771206851, "grad_norm": 1.96875, "kl": 3.8819136679172517, "learning_rate": 1.363566954828754e-06, "loss": 0.1553, "reward": 0.4375, "reward_std": 0.14142135456204413, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1280}, {"completion_length": 80.275, "epoch": 0.6903933636606904, "grad_norm": 24.25, "kl": 4.582204926013946, "learning_rate": 1.3221520393560594e-06, "loss": 0.1833, "reward": 0.43125, "reward_std": 0.07954951152205467, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.43125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1290}, {"completion_length": 166.6125, "epoch": 0.6957452502006958, "grad_norm": 11.9375, "kl": 3.424024987220764, "learning_rate": 1.2811485017410657e-06, "loss": 0.137, "reward": 0.4375, "reward_std": 0.1414213538169861, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.3875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1300}, {"completion_length": 132.9, "epoch": 0.7010971367407011, "grad_norm": 2.5, "kl": 3.937425810098648, "learning_rate": 1.2405706629725814e-06, "loss": 0.1575, "reward": 0.4125, "reward_std": 0.12374368458986282, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1310}, {"completion_length": 74.9625, "epoch": 0.7064490232807065, "grad_norm": 628.0, "kl": 5.910667812824249, "learning_rate": 1.2004326953588672e-06, "loss": 0.2364, "reward": 0.4375, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1320}, {"completion_length": 132.5875, "epoch": 0.7118009098207118, "grad_norm": 26.25, "kl": 3.7988413512706756, "learning_rate": 1.160748617577784e-06, "loss": 0.152, "reward": 0.425, "reward_std": 0.1414213538169861, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1330}, {"completion_length": 137.5875, "epoch": 0.7171527963607172, "grad_norm": 7.0625, "kl": 4.027679702639579, "learning_rate": 1.1215322897805984e-06, "loss": 0.1611, "reward": 0.38125, "reward_std": 0.06187184229493141, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.38125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1340}, {"completion_length": 53.175, "epoch": 0.7225046829007225, "grad_norm": 2.203125, "kl": 4.100459408760071, "learning_rate": 1.082797408751151e-06, "loss": 0.164, "reward": 0.55, "reward_std": 0.1414213538169861, "rewards/correctness_reward_func": 0.1, "rewards/int_reward_func": 0.45, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1350}, {"completion_length": 52.9375, "epoch": 0.7278565694407279, "grad_norm": 16.0, "kl": 4.368235144019127, "learning_rate": 1.044557503122092e-06, "loss": 0.1747, "reward": 0.50625, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.45625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1360}, {"completion_length": 141.875, "epoch": 0.7332084559807333, "grad_norm": 60.0, "kl": 3.3603768765926363, "learning_rate": 1.0068259286498363e-06, "loss": 0.1344, "reward": 0.375, "reward_std": 0.1414213538169861, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1370}, {"completion_length": 101.2625, "epoch": 0.7385603425207385, "grad_norm": 24.25, "kl": 4.070156168937683, "learning_rate": 9.696158635499032e-07, "loss": 0.1628, "reward": 0.41875, "reward_std": 0.07954951152205467, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1380}, {"completion_length": 175.05, "epoch": 0.743912229060744, "grad_norm": 15.8125, "kl": 3.8314215313643216, "learning_rate": 9.329403038942617e-07, "loss": 0.1533, "reward": 0.35625, "reward_std": 0.15026018843054773, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.35625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1390}, {"completion_length": 94.9375, "epoch": 0.7492641156007492, "grad_norm": 5.71875, "kl": 4.3824089646339415, "learning_rate": 8.968120590722951e-07, "loss": 0.1753, "reward": 0.4375, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1400}, {"completion_length": 137.025, "epoch": 0.7546160021407546, "grad_norm": 10.9375, "kl": 4.022230738401413, "learning_rate": 8.612437473169622e-07, "loss": 0.1609, "reward": 0.4, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1410}, {"completion_length": 173.7, "epoch": 0.7599678886807599, "grad_norm": 7.6875, "kl": 3.566299319267273, "learning_rate": 8.262477912977238e-07, "loss": 0.1427, "reward": 0.375, "reward_std": 0.12374368458986282, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1420}, {"completion_length": 69.7, "epoch": 0.7653197752207653, "grad_norm": 25.125, "kl": 3.815484941005707, "learning_rate": 7.918364137817771e-07, "loss": 0.1526, "reward": 0.5375, "reward_std": 0.1590990237891674, "rewards/correctness_reward_func": 0.1, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1430}, {"completion_length": 167.1125, "epoch": 0.7706716617607706, "grad_norm": 2.578125, "kl": 3.2234150230884553, "learning_rate": 7.580216333651005e-07, "loss": 0.1289, "reward": 0.39375, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1440}, {"completion_length": 77.3875, "epoch": 0.776023548300776, "grad_norm": 26.875, "kl": 3.6053977489471434, "learning_rate": 7.248152602748127e-07, "loss": 0.1442, "reward": 0.4625, "reward_std": 0.12374368458986282, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1450}, {"completion_length": 85.6375, "epoch": 0.7813754348407814, "grad_norm": 20.0, "kl": 4.131050819158554, "learning_rate": 6.922288922443055e-07, "loss": 0.1652, "reward": 0.47434999942779543, "reward_std": 0.12282444536685944, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.00625, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.00560000017285347, "step": 1460}, {"completion_length": 70.3875, "epoch": 0.7867273213807867, "grad_norm": 51.25, "kl": 4.598232471942902, "learning_rate": 6.602739104625938e-07, "loss": 0.1839, "reward": 0.49375, "reward_std": 0.13258251994848252, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1470}, {"completion_length": 84.3625, "epoch": 0.7920792079207921, "grad_norm": 2.1875, "kl": 4.117679405212402, "learning_rate": 6.289614755992957e-07, "loss": 0.1647, "reward": 0.4875, "reward_std": 0.07071067690849304, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1480}, {"completion_length": 94.075, "epoch": 0.7974310944607974, "grad_norm": 2.984375, "kl": 4.715282535552978, "learning_rate": 5.983025239066287e-07, "loss": 0.1886, "reward": 0.4625, "reward_std": 0.10606601610779762, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1490}, {"completion_length": 64.5625, "epoch": 0.8027829810008028, "grad_norm": 16.625, "kl": 4.207947021722793, "learning_rate": 5.683077633997944e-07, "loss": 0.1683, "reward": 0.44375, "reward_std": 0.06187184229493141, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.44375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1500}, {"completion_length": 56.0125, "epoch": 0.8081348675408081, "grad_norm": 2.671875, "kl": 4.363655072450638, "learning_rate": 5.389876701170691e-07, "loss": 0.1745, "reward": 0.4875, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4625, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1510}, {"completion_length": 125.9, "epoch": 0.8134867540808135, "grad_norm": 13.6875, "kl": 3.9058427929878237, "learning_rate": 5.103524844609203e-07, "loss": 0.1562, "reward": 0.45, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1520}, {"completion_length": 65.5875, "epoch": 0.8188386406208188, "grad_norm": 10.875, "kl": 4.16762638092041, "learning_rate": 4.824122076214196e-07, "loss": 0.1667, "reward": 0.5, "reward_std": 0.05303300768136978, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.45, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1530}, {"completion_length": 108.9375, "epoch": 0.8241905271608242, "grad_norm": 8.4375, "kl": 4.033653432130814, "learning_rate": 4.55176598083206e-07, "loss": 0.1613, "reward": 0.45, "reward_std": 0.1237436830997467, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1540}, {"completion_length": 134.4, "epoch": 0.8295424137008296, "grad_norm": 3.3125, "kl": 4.833155390620232, "learning_rate": 4.286551682172116e-07, "loss": 0.1933, "reward": 0.45, "reward_std": 0.07071067690849304, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1550}, {"completion_length": 168.625, "epoch": 0.8348943002408349, "grad_norm": 8.25, "kl": 3.783041775226593, "learning_rate": 4.028571809583537e-07, "loss": 0.1513, "reward": 0.45625, "reward_std": 0.20329319462180137, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.38125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1560}, {"completion_length": 121.5125, "epoch": 0.8402461867808403, "grad_norm": 2.765625, "kl": 3.6847407668828964, "learning_rate": 3.7779164657033877e-07, "loss": 0.1474, "reward": 0.44375, "reward_std": 0.20329319760203363, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1570}, {"completion_length": 105.1375, "epoch": 0.8455980733208456, "grad_norm": 101.0, "kl": 4.639008200168609, "learning_rate": 3.534673194987187e-07, "loss": 0.1856, "reward": 0.49375, "reward_std": 0.11490485072135925, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1580}, {"completion_length": 111.3125, "epoch": 0.850949959860851, "grad_norm": 19.0, "kl": 4.4342880845069885, "learning_rate": 3.2989269531328973e-07, "loss": 0.1774, "reward": 0.46875, "reward_std": 0.11490485072135925, "rewards/correctness_reward_func": 0.05, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1590}, {"completion_length": 147.5875, "epoch": 0.8563018464008563, "grad_norm": 24.625, "kl": 3.485319471359253, "learning_rate": 3.070760077409149e-07, "loss": 0.1394, "reward": 0.41875, "reward_std": 0.16793785616755486, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1600}, {"completion_length": 95.3375, "epoch": 0.8616537329408617, "grad_norm": 21.875, "kl": 4.147742259502411, "learning_rate": 2.8502522578979146e-07, "loss": 0.1659, "reward": 0.56875, "reward_std": 0.16793785914778708, "rewards/correctness_reward_func": 0.15, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1610}, {"completion_length": 110.5, "epoch": 0.867005619480867, "grad_norm": 17.75, "kl": 3.8754021763801574, "learning_rate": 2.63748050966178e-07, "loss": 0.155, "reward": 0.4375, "reward_std": 0.1414213538169861, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.4125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1620}, {"completion_length": 105.3875, "epoch": 0.8723575060208724, "grad_norm": 13.4375, "kl": 3.7221854984760285, "learning_rate": 2.4325191458454603e-07, "loss": 0.1489, "reward": 0.4431250005960464, "reward_std": 0.16705397814512252, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.40625, "rewards/soft_format_reward_func": 0.00625, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.005624999850988388, "step": 1630}, {"completion_length": 137.3125, "epoch": 0.8777093925608777, "grad_norm": 16.5, "kl": 3.9831709027290345, "learning_rate": 2.2354397517210663e-07, "loss": 0.1593, "reward": 0.4, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1640}, {"completion_length": 136.225, "epoch": 0.8830612791008831, "grad_norm": 1.671875, "kl": 4.032767236232758, "learning_rate": 2.0463111596860348e-07, "loss": 0.1613, "reward": 0.4, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1650}, {"completion_length": 153.9, "epoch": 0.8884131656408885, "grad_norm": 8.25, "kl": 3.9497052133083344, "learning_rate": 1.8651994252225913e-07, "loss": 0.158, "reward": 0.40625, "reward_std": 0.16793785765767097, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.38125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1660}, {"completion_length": 95.8625, "epoch": 0.8937650521808937, "grad_norm": 11.9375, "kl": 4.006119957566261, "learning_rate": 1.6921678038270412e-07, "loss": 0.1602, "reward": 0.4375, "reward_std": 0.07071067690849304, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1670}, {"completion_length": 115.85, "epoch": 0.8991169387208992, "grad_norm": 29.125, "kl": 4.068606674671173, "learning_rate": 1.5272767289170265e-07, "loss": 0.1627, "reward": 0.49375, "reward_std": 0.22097086608409883, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1680}, {"completion_length": 127.8, "epoch": 0.9044688252609044, "grad_norm": 14.0625, "kl": 4.582245504856109, "learning_rate": 1.3705837907244141e-07, "loss": 0.1833, "reward": 0.41875, "reward_std": 0.16793785765767097, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1690}, {"completion_length": 126.9125, "epoch": 0.9098207118009098, "grad_norm": 11.5, "kl": 3.5307289600372314, "learning_rate": 1.2221437161811788e-07, "loss": 0.1412, "reward": 0.39375, "reward_std": 0.09722718074917794, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.39375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1700}, {"completion_length": 95.0, "epoch": 0.9151725983409151, "grad_norm": 3.765625, "kl": 3.955054688453674, "learning_rate": 1.0820083498053802e-07, "loss": 0.1582, "reward": 0.45, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1710}, {"completion_length": 134.575, "epoch": 0.9205244848809205, "grad_norm": 11.5, "kl": 3.9443099439144134, "learning_rate": 9.50226635593815e-08, "loss": 0.1578, "reward": 0.40625, "reward_std": 0.15026018917560577, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.38125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1720}, {"completion_length": 90.6, "epoch": 0.9258763714209258, "grad_norm": 1.78125, "kl": 4.275810426473617, "learning_rate": 8.268445999277175e-08, "loss": 0.171, "reward": 0.43125, "reward_std": 0.06187184229493141, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.43125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1730}, {"completion_length": 131.3, "epoch": 0.9312282579609312, "grad_norm": 95.0, "kl": 3.58995600650087, "learning_rate": 7.119053354974898e-08, "loss": 0.1436, "reward": 0.475, "reward_std": 0.14142135456204413, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1740}, {"completion_length": 143.7625, "epoch": 0.9365801445009366, "grad_norm": 25.625, "kl": 3.735959368944168, "learning_rate": 6.054489862520552e-08, "loss": 0.1494, "reward": 0.40625, "reward_std": 0.16793785840272904, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.38125, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1750}, {"completion_length": 88.15, "epoch": 0.9419320310409419, "grad_norm": 6.46875, "kl": 3.8297442197799683, "learning_rate": 5.0751273337808214e-08, "loss": 0.1532, "reward": 0.5125, "reward_std": 0.0883883461356163, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.4375, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1760}, {"completion_length": 96.0, "epoch": 0.9472839175809473, "grad_norm": 24.5, "kl": 3.9366838335990906, "learning_rate": 4.181307823139996e-08, "loss": 0.1575, "reward": 0.41875, "reward_std": 0.1149048499763012, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.41875, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1770}, {"completion_length": 145.65, "epoch": 0.9526358041209526, "grad_norm": 12.5, "kl": 3.6378924429416655, "learning_rate": 3.3733435080332834e-08, "loss": 0.1455, "reward": 0.4, "reward_std": 0.10606601536273956, "rewards/correctness_reward_func": 0.0, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1780}, {"completion_length": 84.025, "epoch": 0.957987690660958, "grad_norm": 11.125, "kl": 4.77400826215744, "learning_rate": 2.6515165799151087e-08, "loss": 0.191, "reward": 0.45, "reward_std": 0.14142135232686998, "rewards/correctness_reward_func": 0.025, "rewards/int_reward_func": 0.425, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1790}, {"completion_length": 132.65, "epoch": 0.9633395772009633, "grad_norm": 1.4765625, "kl": 3.372994899749756, "learning_rate": 2.016079145700123e-08, "loss": 0.1349, "reward": 0.475, "reward_std": 0.12374368533492089, "rewards/correctness_reward_func": 0.075, "rewards/int_reward_func": 0.4, "rewards/soft_format_reward_func": 0.0, "rewards/strict_format_reward_func": 0.0, "rewards/xmlcount_reward_func": 0.0, "step": 1800}], "logging_steps": 10, "max_steps": 1868, "num_input_tokens_seen": 0, "num_train_epochs": 1, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}