{"time":"2025-03-02T18:59:56.281473486+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.7","symlink path":"/home/<USER>/workspaces/pytest/wandb/run-20250302_185956-et23bz8x/logs/debug-core.log"}
{"time":"2025-03-02T18:59:56.407463578+08:00","level":"INFO","msg":"created new stream","id":"et23bz8x"}
{"time":"2025-03-02T18:59:56.407496686+08:00","level":"INFO","msg":"stream: started","id":"et23bz8x"}
{"time":"2025-03-02T18:59:56.407530785+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"et23bz8x"}
{"time":"2025-03-02T18:59:56.407564993+08:00","level":"INFO","msg":"handler: started","stream_id":"et23bz8x"}
{"time":"2025-03-02T18:59:56.407615847+08:00","level":"INFO","msg":"sender: started","stream_id":"et23bz8x"}
{"time":"2025-03-02T18:59:57.09763881+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-03-02T20:19:50.050682856+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T20:20:23.36556176+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T20:21:00.510627207+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T20:21:43.190273058+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T20:22:32.42647993+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T20:23:42.626556246+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T20:25:20.49684665+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T20:26:58.552324591+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T20:28:34.042035307+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T20:30:05.587778475+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.001055994,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"mge16a3ptvfz\"  connection_id:\"127.0.0.1:59020\")"}
{"time":"2025-03-02T20:30:12.466024607+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T20:31:51.899357532+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T20:32:14.91257945+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000423737,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T20:32:14.912657752+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000720082,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T20:32:14.912745463+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000432473,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T20:32:14.924035359+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000405083,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T20:32:27.797137936+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000429363,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T20:32:33.372914194+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000580733,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:59020\")"}
{"time":"2025-03-02T20:33:27.320152733+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T20:35:06.354217007+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T20:36:12.157570307+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":937.445560443,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"mge16a3ptvfz\"  connection_id:\"127.0.0.1:59020\")"}
{"time":"2025-03-02T20:36:12.157673819+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":817.552571082,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T20:36:12.157683167+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":817.540886686,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T20:36:12.157744712+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":801.977522581,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:59020\")"}
{"time":"2025-03-02T20:36:12.157770975+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":817.552455337,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T20:36:12.157776414+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":817.552312795,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T20:36:12.157721947+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":807.553101444,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-02T23:06:53.39726584+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T23:07:27.147175697+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T23:08:01.499326915+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T23:08:40.505080027+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T23:09:28.945449579+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-02T23:10:38.77259831+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T23:12:08.774572528+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T23:13:38.77607394+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-02T23:15:08.776927194+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
