datasets==3.2.0
pyzmq==26.2.1
mpmath==1.3.0
charset-normalizer==3.4.1
fastapi==0.115.8
smmap==5.0.2
shellingham==1.5.4
trl==0.15.1
platformdirs==4.3.6
isoduration==20.11.0
mistune==3.1.2
referencing==0.36.2
ipython==8.32.0
websocket-client==1.8.0
nvidia-cuda-runtime-cu12==12.4.127
sympy==1.13.1
markdown-it-py==3.0.0
urllib3==2.3.0
setproctitle==1.3.4
packaging==24.2
Pygments==2.19.1
audioread==3.0.1
soundfile==0.13.1
gradio_client==1.5.4
rpds-py==0.23.1
aiohttp==3.11.12
certifi==2025.1.31
webcolors==24.11.1
aiosignal==1.3.2
jsonschema==4.23.0
h11==0.14.0
jupyter_client==8.6.3
numpy==1.26.4
yarl==1.18.3
beautifulsoup4==4.13.3
python-dateutil==2.9.0.post0
pydantic_core==2.27.2
numba==0.61.0
cycler==0.12.1
sniffio==1.3.1
llvmlite==0.44.0
jupyter-events==0.12.0
safetensors==0.5.2
nvidia-curand-cu12==**********
soupsieve==2.6
dill==0.3.8
anyio==4.8.0
traitlets==5.14.3
stack-data==0.6.3
tornado==6.4.2
filelock==3.17.0
jupyter_core==5.7.2
accelerate==1.2.1
async-lru==2.0.4
fsspec==2024.9.0
nvidia-cusolver-cu12==********
tyro==0.8.14
argon2-cffi==23.1.0
annotated-types==0.7.0
ffmpy==0.5.0
nltk==3.9.1
orjson==3.10.15
nvidia-nvjitlink-cu12==12.4.127
tomlkit==0.13.2
ptyprocess==0.7.0
pip==24.0
jedi==0.19.2
nest-asyncio==1.6.0
notebook_shim==0.2.4
matplotlib==3.10.0
fastjsonschema==2.21.1
docker-pycreds==0.4.0
parso==0.8.4
asttokens==3.0.0
pandocfilters==1.5.1
fire==0.7.0
jupyterlab==4.3.5
click==8.1.8
setuptools==75.8.0
pytz==2025.1
requests==2.32.3
semantic-version==2.10.0
rich==13.9.4
comm==0.2.2
frozenlist==1.5.0
types-python-dateutil==2.9.0.20241206
peft==0.12.0
sse-starlette==2.2.1
terminado==0.18.1
aiohappyeyeballs==2.4.6
jupyter-lsp==2.2.5
scikit-learn==1.6.1
Jinja2==3.1.5
six==1.17.0
babel==2.17.0
termcolor==2.5.0
nvidia-cusparse-cu12==**********
wcwidth==0.2.13
arrow==1.3.0
pyarrow==19.0.1
triton==3.2.0
GitPython==3.1.44
pydub==0.25.1
ipykernel==6.29.5
aiofiles==23.2.1
tzdata==2025.1
gradio==5.12.0
cffi==1.17.1
starlette==0.45.3
pillow==11.1.0
tinycss2==1.4.0
psutil==7.0.0
pandas==2.2.3
tokenizers==0.21.0
pydantic==2.10.6
fonttools==4.56.0
typing_extensions==4.12.2
pycparser==2.22
einops==0.8.1
nvidia-cublas-cu12==********
attrs==25.1.0
mdurl==0.1.2
safehttpx==0.1.6
xxhash==3.5.0
msgpack==1.1.0
argon2-cffi-bindings==21.2.0
torch==2.6.0+cu124
prometheus_client==0.21.1
threadpoolctl==3.5.0
huggingface-hub==0.29.1
nbformat==5.10.4
idna==3.10
json5==0.10.0
multiprocess==0.70.16
fqdn==1.5.1
python-multipart==0.0.20
ruff==0.9.7
nvidia-cudnn-cu12==********
wandb==0.19.7
nvidia-cusparselt-cu12==0.6.2
networkx==3.4.2
prompt_toolkit==3.0.50
MarkupSafe==2.1.5
uvicorn==0.34.0
nvidia-nvtx-cu12==12.4.127
propcache==0.3.0
rfc3986-validator==0.1.1
contourpy==1.3.1
llamafactory==0.9.2.dev0
pyparsing==3.2.1
rfc3339-validator==0.1.4
jupyter_server==2.15.0
lazy_loader==0.4
librosa==0.10.2.post1
debugpy==1.8.12
pooch==1.8.2
gitdb==4.0.12
matplotlib-inline==0.1.7
tqdm==4.67.1
pexpect==4.9.0
nvidia-nccl-cu12==2.21.5
flash_attn==2.7.4.post1
kiwisolver==1.4.8
bleach==6.2.0
jieba==0.42.1
overrides==7.7.0
jupyter_server_terminals==0.5.3
docstring_parser==0.16
shtab==1.7.1
transformers==4.49.0
nbclient==0.10.2
httpcore==1.0.7
rouge-chinese==1.0.3
webencodings==0.5.1
soxr==0.5.0.post1
jsonschema-specifications==2024.10.1
defusedxml==0.7.1
nvidia-cuda-cupti-cu12==12.4.127
sentry-sdk==2.22.0
typer==0.15.1
tiktoken==0.9.0
jupyterlab_server==2.27.3
python-json-logger==3.2.1
websockets==14.2
scipy==1.15.2
regex==2024.11.6
nvidia-cuda-nvrtc-cu12==12.4.127
jsonpointer==3.0.0
joblib==1.4.2
pure_eval==0.2.3
PyYAML==6.0.2
uri-template==1.3.0
httpx==0.28.1
Send2Trash==1.8.3
nvidia-cufft-cu12==********
decorator==5.2.0
notebook==7.3.2
av==14.1.0
jupyterlab_pygments==0.3.0
protobuf==5.29.3
nbconvert==7.16.6
sentencepiece==0.2.0
multidict==6.1.0
executing==2.2.0
llamafactory==0.9.2.dev0
jaraco.collections==5.1.0
jaraco.functools==4.0.1
packaging==24.2
autocommand==2.2.2
zipp==3.19.2
typing_extensions==4.12.2
backports.tarfile==1.2.0
jaraco.text==3.12.1
importlib_metadata==8.0.0
more-itertools==10.3.0
jaraco.context==5.3.0
platformdirs==4.2.2
inflect==7.3.1
tomli==2.0.1
wheel==0.43.0
typeguard==4.3.0
