{"os": "Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39", "python": "CPython 3.12.3", "startedAt": "2025-03-04T15:34:19.182759Z", "program": "/home/<USER>/workspaces/pytest/grpo_demo.py", "codePath": "grpo_demo.py", "email": "<EMAIL>", "root": "/home/<USER>/workspaces/pytest", "host": "HuangXi", "executable": "/home/<USER>/workspaces/llf/bin/python3", "codePathLocal": "grpo_demo.py", "cpu_count": 6, "cpu_count_logical": 12, "gpu": "NVIDIA GeForce RTX 4080 SUPER", "gpu_count": 1, "disk": {"/": {"total": "1081101176832", "used": "51617161216"}}, "memory": {"total": "32710496256"}, "cpu": {"count": 6, "countLogical": 12}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4080 SUPER", "memoryTotal": "17171480576", "architecture": "Ada"}], "cudaVersion": "12.7"}