{"time":"2025-03-04T23:34:19.184683823+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.7","symlink path":"/home/<USER>/workspaces/pytest/wandb/run-20250304_233419-qjl1ls1s/logs/debug-core.log"}
{"time":"2025-03-04T23:34:19.318341773+08:00","level":"INFO","msg":"created new stream","id":"qjl1ls1s"}
{"time":"2025-03-04T23:34:19.318383752+08:00","level":"INFO","msg":"stream: started","id":"qjl1ls1s"}
{"time":"2025-03-04T23:34:19.318415162+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"qjl1ls1s"}
{"time":"2025-03-04T23:34:19.318438132+08:00","level":"INFO","msg":"handler: started","stream_id":"qjl1ls1s"}
{"time":"2025-03-04T23:34:19.318472294+08:00","level":"INFO","msg":"sender: started","stream_id":"qjl1ls1s"}
{"time":"2025-03-04T23:34:20.776252488+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-03-05T03:38:11.848006416+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/qjl1ls1s/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-03-05T04:58:50.094722014+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T07:38:22.855440802+08:00","level":"INFO","msg":"stream: closing","id":"qjl1ls1s"}
{"time":"2025-03-05T07:38:22.85546014+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-03-05T07:38:22.918293804+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-03-05T07:38:40.12278686+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-03-05T07:38:40.761991466+08:00","level":"INFO","msg":"handler: closed","stream_id":"qjl1ls1s"}
{"time":"2025-03-05T07:38:40.762014344+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"qjl1ls1s"}
{"time":"2025-03-05T07:38:40.762027605+08:00","level":"INFO","msg":"sender: closed","stream_id":"qjl1ls1s"}
{"time":"2025-03-05T07:38:40.762107629+08:00","level":"INFO","msg":"stream: closed","id":"qjl1ls1s"}
