2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_setup.py:_flush():67] Current SDK version is 0.19.7
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_setup.py:_flush():67] Configure stats pid to 21171
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/.config/wandb/settings
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/workspaces/pytest/wandb/settings
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_init.py:setup_run_log_directory():647] Logging user logs to /home/<USER>/workspaces/pytest/wandb/run-20250304_233419-qjl1ls1s/logs/debug.log
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_init.py:setup_run_log_directory():648] Logging internal logs to /home/<USER>/workspaces/pytest/wandb/run-20250304_233419-qjl1ls1s/logs/debug-internal.log
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_init.py:init():761] calling init triggers
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_init.py:init():766] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_init.py:init():784] starting backend
2025-03-04 23:34:19,179 INFO    MainThread:21171 [wandb_init.py:init():788] sending inform_init request
2025-03-04 23:34:19,182 INFO    MainThread:21171 [backend.py:_multiprocessing_setup():97] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-03-04 23:34:19,182 INFO    MainThread:21171 [wandb_init.py:init():803] backend started and connected
2025-03-04 23:34:19,183 INFO    MainThread:21171 [wandb_init.py:init():896] updated telemetry
2025-03-04 23:34:19,183 INFO    MainThread:21171 [wandb_init.py:init():920] communicating run to backend with 90.0 second timeout
2025-03-04 23:34:20,725 INFO    MainThread:21171 [wandb_init.py:init():995] starting run threads in backend
2025-03-04 23:34:20,888 INFO    MainThread:21171 [wandb_run.py:_console_start():2377] atexit reg
2025-03-04 23:34:20,888 INFO    MainThread:21171 [wandb_run.py:_redirect():2227] redirect: wrap_raw
2025-03-04 23:34:20,888 INFO    MainThread:21171 [wandb_run.py:_redirect():2292] Wrapping output streams.
2025-03-04 23:34:20,888 INFO    MainThread:21171 [wandb_run.py:_redirect():2317] Redirects installed.
2025-03-04 23:34:20,890 INFO    MainThread:21171 [wandb_init.py:init():1037] run started, returning control to user process
2025-03-04 23:34:20,891 INFO    MainThread:21171 [wandb_run.py:_config_callback():1261] config_cb None None {'vocab_size': 151936, 'max_position_embeddings': 32768, 'hidden_size': 896, 'intermediate_size': 4864, 'num_hidden_layers': 24, 'num_attention_heads': 14, 'use_sliding_window': False, 'sliding_window': 32768, 'max_window_layers': 21, 'num_key_value_heads': 2, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-06, 'use_cache': False, 'rope_theta': 1000000.0, 'rope_scaling': None, 'attention_dropout': 0.0, 'return_dict': True, 'output_hidden_states': False, 'output_attentions': False, 'torchscript': False, 'torch_dtype': 'bfloat16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': True, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 0.9, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['Qwen2ForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 151643, 'pad_token_id': None, 'eos_token_id': 151645, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': '/home/<USER>/workspaces/LLaMA-Factory/output/qwen25_05b_lora_sft', '_attn_implementation_autoset': True, 'transformers_version': '4.49.0', 'model_type': 'qwen2', 'output_dir': './output/Qwen-0.5B-GRPO', 'overwrite_output_dir': False, 'do_train': False, 'do_eval': False, 'do_predict': False, 'eval_strategy': 'no', 'prediction_loss_only': False, 'per_device_train_batch_size': 4, 'per_device_eval_batch_size': 8, 'per_gpu_train_batch_size': None, 'per_gpu_eval_batch_size': None, 'gradient_accumulation_steps': 2, 'eval_accumulation_steps': None, 'eval_delay': 0, 'torch_empty_cache_steps': None, 'learning_rate': 5e-06, 'weight_decay': 0.1, 'adam_beta1': 0.9, 'adam_beta2': 0.99, 'adam_epsilon': 1e-08, 'max_grad_norm': 0.1, 'num_train_epochs': 1, 'max_steps': -1, 'lr_scheduler_type': 'cosine', 'lr_scheduler_kwargs': {}, 'warmup_ratio': 0.1, 'warmup_steps': 0, 'log_level': 'passive', 'log_level_replica': 'warning', 'log_on_each_node': False, 'logging_dir': './output/Qwen-0.5B-GRPO/runs/Mar04_23-34-16_HuangXi', 'logging_strategy': 'steps', 'logging_first_step': False, 'logging_steps': 10, 'logging_nan_inf_filter': True, 'save_strategy': 'steps', 'save_steps': 100, 'save_total_limit': None, 'save_safetensors': True, 'save_on_each_node': False, 'save_only_model': False, 'restore_callback_states_from_checkpoint': False, 'no_cuda': False, 'use_cpu': False, 'use_mps_device': False, 'seed': 42, 'data_seed': None, 'jit_mode_eval': False, 'use_ipex': False, 'bf16': True, 'fp16': False, 'fp16_opt_level': 'O1', 'half_precision_backend': 'auto', 'bf16_full_eval': False, 'fp16_full_eval': False, 'tf32': None, 'local_rank': 0, 'ddp_backend': None, 'tpu_num_cores': None, 'tpu_metrics_debug': False, 'debug': [], 'dataloader_drop_last': False, 'eval_steps': None, 'dataloader_num_workers': 0, 'dataloader_prefetch_factor': None, 'past_index': -1, 'run_name': 'Qwen-1.5B-GRPO-gsm8k', 'disable_tqdm': False, 'remove_unused_columns': False, 'label_names': None, 'load_best_model_at_end': False, 'metric_for_best_model': None, 'greater_is_better': None, 'ignore_data_skip': False, 'fsdp': [], 'fsdp_min_num_params': 0, 'fsdp_config': {'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False}, 'fsdp_transformer_layer_cls_to_wrap': None, 'accelerator_config': {'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None}, 'deepspeed': None, 'label_smoothing_factor': 0.0, 'optim': 'adamw_torch', 'optim_args': None, 'adafactor': False, 'group_by_length': False, 'length_column_name': 'length', 'report_to': ['wandb'], 'ddp_find_unused_parameters': None, 'ddp_bucket_cap_mb': None, 'ddp_broadcast_buffers': None, 'dataloader_pin_memory': True, 'dataloader_persistent_workers': False, 'skip_memory_metrics': True, 'use_legacy_prediction_loop': False, 'push_to_hub': False, 'resume_from_checkpoint': None, 'hub_model_id': None, 'hub_strategy': 'every_save', 'hub_token': '<HUB_TOKEN>', 'hub_private_repo': None, 'hub_always_push': False, 'gradient_checkpointing': False, 'gradient_checkpointing_kwargs': None, 'include_inputs_for_metrics': False, 'include_for_metrics': [], 'eval_do_concat_batches': True, 'fp16_backend': 'auto', 'evaluation_strategy': None, 'push_to_hub_model_id': None, 'push_to_hub_organization': None, 'push_to_hub_token': '<PUSH_TO_HUB_TOKEN>', 'mp_parameters': '', 'auto_find_batch_size': False, 'full_determinism': False, 'torchdynamo': None, 'ray_scope': 'last', 'ddp_timeout': 1800, 'torch_compile': False, 'torch_compile_backend': None, 'torch_compile_mode': None, 'dispatch_batches': None, 'split_batches': None, 'include_tokens_per_second': False, 'include_num_input_tokens_seen': False, 'neftune_noise_alpha': None, 'optim_target_modules': None, 'batch_eval_metrics': False, 'eval_on_start': False, 'use_liger_kernel': False, 'eval_use_gather_object': False, 'average_tokens_across_devices': False, 'model_init_kwargs': None, 'max_prompt_length': 128, 'num_generations': 2, 'max_completion_length': 1000, 'ds3_gather_for_generation': True, 'use_vllm': False, 'vllm_device': 'auto', 'vllm_gpu_memory_utilization': 0.9, 'vllm_dtype': 'auto', 'vllm_max_model_len': None, 'beta': 0.04, 'reward_weights': None, 'sync_ref_model': False, 'ref_model_mixup_alpha': 0.9, 'ref_model_sync_steps': 64, 'log_completions': False}
2025-03-04 23:34:20,892 INFO    MainThread:21171 [wandb_config.py:__setitem__():154] config set model/num_parameters = 494032768 - <bound method Run._config_callback of <wandb.sdk.wandb_run.Run object at 0x7f118acb81d0>>
2025-03-04 23:34:20,892 INFO    MainThread:21171 [wandb_run.py:_config_callback():1261] config_cb model/num_parameters 494032768 None
2025-03-05 07:38:22,855 WARNING MsgRouterThr:21171 [router.py:message_loop():77] message_loop has been closed
Traceback (most recent call last):
  File "/home/<USER>/workspaces/llf/lib/python3.12/site-packages/wandb/sdk/interface/router_sock.py", line 28, in _read_message
    resp = self._sock_client.read_server_response(timeout=1)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspaces/llf/lib/python3.12/site-packages/wandb/sdk/lib/sock_client.py", line 285, in read_server_response
    data = self._read_packet_bytes(timeout=timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspaces/llf/lib/python3.12/site-packages/wandb/sdk/lib/sock_client.py", line 270, in _read_packet_bytes
    raise SockClientClosedError
wandb.sdk.lib.sock_client.SockClientClosedError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/workspaces/llf/lib/python3.12/site-packages/wandb/sdk/interface/router.py", line 70, in message_loop
    msg = self._read_message()
          ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspaces/llf/lib/python3.12/site-packages/wandb/sdk/interface/router_sock.py", line 30, in _read_message
    raise MessageRouterClosedError from e
wandb.sdk.interface.router.MessageRouterClosedError
2025-03-05 07:38:22,856 INFO    MsgRouterThr:21171 [mailbox.py:close():115] Closing mailbox, abandoning 1 handles.
