{"time":"2025-03-03T22:36:27.413448061+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.7","symlink path":"/home/<USER>/workspaces/pytest/wandb/run-20250303_223627-rv8s7zo1/logs/debug-core.log"}
{"time":"2025-03-03T22:36:27.54772944+08:00","level":"INFO","msg":"created new stream","id":"rv8s7zo1"}
{"time":"2025-03-03T22:36:27.547759809+08:00","level":"INFO","msg":"stream: started","id":"rv8s7zo1"}
{"time":"2025-03-03T22:36:27.547785028+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"rv8s7zo1"}
{"time":"2025-03-03T22:36:27.547819487+08:00","level":"INFO","msg":"sender: started","stream_id":"rv8s7zo1"}
{"time":"2025-03-03T22:36:27.547848406+08:00","level":"INFO","msg":"handler: started","stream_id":"rv8s7zo1"}
{"time":"2025-03-03T22:36:28.55364297+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-03-03T23:29:29.843249818+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T07:53:25.038506966+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T07:53:58.543020425+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T07:54:33.499929822+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T07:55:13.397866383+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T07:56:05.347557949+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T07:57:17.131660913+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T07:58:50.262922126+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T08:00:23.333505547+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T08:01:55.689733383+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T08:03:14.371423987+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000568657,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"pul456k5wjax\"  connection_id:\"127.0.0.1:52608\")"}
{"time":"2025-03-04T08:03:29.335432307+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T08:05:03.127197994+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T08:05:38.274236438+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000346924,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T08:05:38.274311704+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000455303,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T08:05:38.274236448+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000061562,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T08:05:38.274241242+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000049652,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T08:05:38.286383082+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000481193,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T08:06:36.494951726+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T08:06:58.159239787+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000632744,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:52608\")"}
{"time":"2025-03-04T08:08:08.820217152+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T08:09:12.282186616+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":945.525639144,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"pul456k5wjax\"  connection_id:\"127.0.0.1:52608\")"}
{"time":"2025-03-04T08:09:12.282373731+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":806.42803811,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T08:09:12.282397237+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":806.427719332,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T08:09:12.282410778+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":806.428100777,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T08:09:12.282395387+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":806.427734757,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T08:09:12.282359471+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":728.893975888,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:52608\")"}
{"time":"2025-03-04T08:09:12.282426463+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":806.416019419,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T17:48:06.229571867+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T17:48:38.328905795+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T17:49:12.612427117+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T17:49:51.389407435+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T17:50:38.52952095+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T17:51:43.849075413+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T17:53:13.84974203+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T17:54:43.851184326+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T17:56:13.852438842+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T17:57:36.229237448+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000763299,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"l7wyrs4q9o43\"  connection_id:\"127.0.0.1:52608\")"}
{"time":"2025-03-04T17:57:43.854380029+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T17:59:13.855835724+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T18:00:25.546490322+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000191722,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:52608\")"}
{"time":"2025-03-04T18:00:30.237543473+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.00081808,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T18:00:30.23756395+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000608063,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T18:00:30.237543417+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000543671,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T18:00:30.237619192+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000366589,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T18:00:33.185001629+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000495943,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T18:00:43.85748977+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-04T18:02:13.858403819+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-04T18:03:14.485176555+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":938.256939211,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"l7wyrs4q9o43\"  connection_id:\"127.0.0.1:52608\")"}
{"time":"2025-03-04T18:03:14.485239489+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":764.248531431,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T18:03:14.485290647+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":768.939042025,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:52608\")"}
{"time":"2025-03-04T18:03:14.485300419+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":764.248351905,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T18:03:14.485362861+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":761.30080671,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T18:03:14.485380492+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":764.248063357,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-04T18:03:14.485457468+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":764.248350203,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
                                                                                                                                                                                                                                                                                                