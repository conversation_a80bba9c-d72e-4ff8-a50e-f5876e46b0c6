{"time":"2025-02-22T23:29:46.706358201+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.7","symlink path":"/home/<USER>/pytest/wandb/run-20250222_232946-oqdcos0i/logs/debug-core.log"}
{"time":"2025-02-22T23:29:46.852128354+08:00","level":"INFO","msg":"created new stream","id":"oqdcos0i"}
{"time":"2025-02-22T23:29:46.852186285+08:00","level":"INFO","msg":"stream: started","id":"oqdcos0i"}
{"time":"2025-02-22T23:29:46.852508041+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"oqdcos0i"}
{"time":"2025-02-22T23:29:46.852829495+08:00","level":"INFO","msg":"sender: started","stream_id":"oqdcos0i"}
{"time":"2025-02-22T23:29:46.853635685+08:00","level":"INFO","msg":"handler: started","stream_id":"oqdcos0i"}
{"time":"2025-02-22T23:29:52.229442269+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-02-22T23:54:16.171815558+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/oqdcos0i/file_stream\": read tcp ************:39066->*************:443: read: connection reset by peer"}
{"time":"2025-02-23T08:39:37.31295418+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-02-23T08:40:09.327239743+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-02-23T08:40:44.197581626+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-02-23T08:41:22.590691705+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-02-23T08:42:09.551849574+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-02-23T08:43:16.306368074+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-02-23T08:44:47.9177356+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-02-23T08:46:19.068040909+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-02-23T08:47:50.867820016+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-02-23T08:49:15.561094223+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.00051226,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"e82r3a9u3nzn\"  connection_id:\"127.0.0.1:34338\")"}
{"time":"2025-02-23T08:49:22.759304607+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-02-23T08:50:54.566330373+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-02-23T08:50:59.701246776+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000056371,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:34338\")"}
{"time":"2025-02-23T08:51:02.310919616+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000400554,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T08:51:02.310932084+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000528527,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T08:51:02.310919714+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000084006,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T08:51:02.311058091+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000732209,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T08:51:02.315053538+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000832181,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T08:52:26.648994727+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-02-23T08:53:58.253955249+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-02-23T08:55:27.244196059+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": read tcp ************:48480->*************:443: read: connection timed out"}
{"time":"2025-02-23T08:56:29.716969745+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":1024.668947832,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"e82r3a9u3nzn\"  connection_id:\"127.0.0.1:34338\")"}
{"time":"2025-02-23T08:56:29.71724563+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":919.722542536,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T08:56:29.71714678+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":919.722853205,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T08:56:29.717143319+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":922.332169815,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:34338\")"}
{"time":"2025-02-23T08:56:29.719126676+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":919.723141922,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T08:56:29.717074748+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":919.718992812,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T08:56:29.717296276+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":919.723141158,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-02-23T09:05:36.650765265+08:00","level":"INFO","msg":"stream: closing","id":"oqdcos0i"}
{"time":"2025-02-23T09:05:36.65157773+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-02-23T09:05:36.657410089+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-02-23T09:05:38.401245441+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-02-23T09:05:39.271912069+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"oqdcos0i"}
{"time":"2025-02-23T09:05:39.271983113+08:00","level":"INFO","msg":"handler: closed","stream_id":"oqdcos0i"}
{"time":"2025-02-23T09:05:39.271986812+08:00","level":"INFO","msg":"sender: closed","stream_id":"oqdcos0i"}
{"time":"2025-02-23T09:05:39.272164549+08:00","level":"INFO","msg":"stream: closed","id":"oqdcos0i"}
