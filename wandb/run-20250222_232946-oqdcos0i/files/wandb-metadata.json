{"os": "Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39", "python": "CPython 3.12.3", "startedAt": "2025-02-22T15:29:46.704770Z", "program": "/home/<USER>/pytest/grpo_demo.py", "codePath": "grpo_demo.py", "email": "<EMAIL>", "root": "/home/<USER>/pytest", "host": "HuangXi", "executable": "/home/<USER>/llmft/bin/python3", "codePathLocal": "grpo_demo.py", "cpu_count": 12, "cpu_count_logical": 24, "gpu": "NVIDIA GeForce RTX 4080 SUPER", "gpu_count": 1, "disk": {"/": {"total": "1081101176832", "used": "19511664640"}}, "memory": {"total": "16294359040"}, "cpu": {"count": 12, "countLogical": 24}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4080 SUPER", "memoryTotal": "17171480576", "architecture": "Ada"}], "cudaVersion": "12.7"}