mpmath==1.3.0
charset-normalizer==3.4.1
smmap==5.0.2
trl==0.15.1
platformdirs==4.3.6
nvidia-cuda-runtime-cu12==12.4.127
sympy==1.13.1
markdown-it-py==3.0.0
urllib3==2.3.0
setproctitle==1.3.4
packaging==24.2
Pygments==2.19.1
aiohttp==3.11.12
certifi==2025.1.31
aiosignal==1.3.2
yarl==1.18.3
python-dateutil==2.9.0.post0
pydantic_core==2.27.2
safetensors==0.5.2
nvidia-curand-cu12==**********
dill==0.3.8
peft==0.14.0
filelock==3.17.0
nvidia-cusolver-cu12==********
annotated-types==0.7.0
nvidia-nvjitlink-cu12==12.4.127
pip==24.0
docker-pycreds==0.4.0
click==8.1.8
setuptools==75.8.0
pytz==2025.1
requests==2.32.3
rich==13.9.4
frozenlist==1.5.0
aiohappyeyeballs==2.4.6
Jinja2==3.1.5
six==1.17.0
nvidia-cusparse-cu12==**********
pyarrow==19.0.1
triton==3.2.0
GitPython==3.1.44
tzdata==2025.1
psutil==7.0.0
pandas==2.2.3
tokenizers==0.21.0
MarkupSafe==3.0.2
pydantic==2.10.6
typing_extensions==4.12.2
einops==0.8.1
nvidia-cublas-cu12==********
attrs==25.1.0
mdurl==0.1.2
datasets==3.3.2
xxhash==3.5.0
torch==2.6.0+cu124
huggingface-hub==0.29.1
idna==3.10
numpy==2.2.3
multiprocess==0.70.16
nvidia-cudnn-cu12==********
fsspec==2024.12.0
wandb==0.19.7
nvidia-cusparselt-cu12==0.6.2
networkx==3.4.2
nvidia-nvtx-cu12==12.4.127
propcache==0.3.0
gitdb==4.0.12
tqdm==4.67.1
nvidia-nccl-cu12==2.21.5
flash_attn==2.7.4.post1
transformers==4.49.0
nvidia-cuda-cupti-cu12==12.4.127
sentry-sdk==2.22.0
regex==2024.11.6
nvidia-cuda-nvrtc-cu12==12.4.127
accelerate==1.4.0
PyYAML==6.0.2
nvidia-cufft-cu12==********
protobuf==5.29.3
multidict==6.1.0
jaraco.collections==5.1.0
jaraco.functools==4.0.1
packaging==24.2
autocommand==2.2.2
zipp==3.19.2
typing_extensions==4.12.2
backports.tarfile==1.2.0
jaraco.text==3.12.1
importlib_metadata==8.0.0
more-itertools==10.3.0
jaraco.context==5.3.0
platformdirs==4.2.2
inflect==7.3.1
tomli==2.0.1
wheel==0.43.0
typeguard==4.3.0
