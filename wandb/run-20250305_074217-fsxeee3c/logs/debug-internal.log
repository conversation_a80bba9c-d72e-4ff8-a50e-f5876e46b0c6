{"time":"2025-03-05T07:42:17.341337174+08:00","level":"INFO","msg":"stream: starting","core version":"0.19.7","symlink path":"/home/<USER>/workspaces/pytest/wandb/run-20250305_074217-fsxeee3c/logs/debug-core.log"}
{"time":"2025-03-05T07:42:17.469489199+08:00","level":"INFO","msg":"created new stream","id":"fsxeee3c"}
{"time":"2025-03-05T07:42:17.469525244+08:00","level":"INFO","msg":"stream: started","id":"fsxeee3c"}
{"time":"2025-03-05T07:42:17.469557931+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"fsxeee3c"}
{"time":"2025-03-05T07:42:17.469585518+08:00","level":"INFO","msg":"sender: started","stream_id":"fsxeee3c"}
{"time":"2025-03-05T07:42:17.469613345+08:00","level":"INFO","msg":"handler: started","stream_id":"fsxeee3c"}
{"time":"2025-03-05T07:42:18.197177471+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-03-05T08:19:35.417710688+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:20:06.441366187+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:20:07.642775104+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-03-05T08:20:39.460198684+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:21:44.625628365+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:22:05.706992832+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-03-05T08:22:15.234228269+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T08:22:31.367904022+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-03-05T08:22:47.942954427+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:23:22.282549407+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-03-05T08:23:53.65894864+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T08:24:19.598393345+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-03-05T08:24:24.580589168+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:24:57.879129883+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:25:55.971060545+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-03-05T08:26:02.85509353+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T08:26:33.760753439+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:26:58.571916696+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-03-05T08:28:11.959759241+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:28:30.798568605+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-03-05T08:28:42.488356616+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:29:13.869207102+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:30:19.504250228+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T08:30:36.951567566+08:00","level":"INFO","msg":"api: retrying HTTP error","status":429,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"rate limit exceeded\"}"}
{"time":"2025-03-05T08:30:50.063383309+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:31:07.902472371+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-03-05T08:31:22.76337301+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T08:31:26.638250434+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-03-05T08:32:00.503763182+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T08:32:32.127689998+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-03-05T08:32:45.8438812+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:32:55.128600252+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-03-05T08:33:50.317390721+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T08:36:04.256478181+08:00","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-03-05T08:36:37.178750066+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T08:37:07.666737496+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T09:54:32.41984117+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T09:55:02.768325777+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T09:55:35.565940524+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T09:56:12.274589519+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T09:56:58.506073393+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T09:58:02.802599345+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T09:59:27.860384489+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T10:00:52.700134656+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T10:02:19.270634798+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T10:03:32.047928992+08:00","level":"WARN","msg":"sender: taking a long time","seconds":600.000830221,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"6ntzc3mfil7p\"  connection_id:\"127.0.0.1:35764\")"}
{"time":"2025-03-05T10:03:44.180461076+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T10:05:09.008625383+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T10:05:57.051818204+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000679592,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:05:57.051944067+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.000619162,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:05:57.051841155+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.001057732,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:05:57.062111793+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.001031163,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:06:07.052606535+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.00101798,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:06:33.840525857+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-03-05T10:06:42.237214583+08:00","level":"WARN","msg":"runwork: taking a long time","seconds":600.004915832,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:35764\")"}
{"time":"2025-03-05T10:07:58.681482277+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-03-05T10:08:57.410780432+08:00","level":"INFO","msg":"sender: succeeded after taking longer than expected","seconds":942.787945236,"work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true  mailbox_slot:\"6ntzc3mfil7p\"  connection_id:\"127.0.0.1:35764\")"}
{"time":"2025-03-05T10:08:57.411003711+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":789.070077369,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:08:57.410962465+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":789.059758984,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:08:57.410970646+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":779.069280077,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:08:57.410979874+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":789.069558929,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:08:57.410962481+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":789.06968525,"work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)"}
{"time":"2025-03-05T10:08:57.411028015+08:00","level":"INFO","msg":"runwork: succeeded after taking longer than expected","seconds":742.246461629,"work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:35764\")"}
{"time":"2025-03-06T01:34:04.268519165+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": read tcp ************:54302->*************:443: read: connection reset by peer"}
{"time":"2025-03-06T19:57:41.298415972+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/kenryhuang-alipay/huggingface/fsxeee3c/file_stream\": dial tcp *************:443: connect: connection timed out"}
{"time":"2025-03-06T23:31:51.772644793+08:00","level":"INFO","msg":"stream: closing","id":"fsxeee3c"}
{"time":"2025-03-06T23:31:51.772955552+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-03-06T23:31:51.778943356+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-03-06T23:31:56.491477045+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-03-06T23:31:57.095749686+08:00","level":"INFO","msg":"handler: closed","stream_id":"fsxeee3c"}
{"time":"2025-03-06T23:31:57.095858281+08:00","level":"INFO","msg":"sender: closed","stream_id":"fsxeee3c"}
{"time":"2025-03-06T23:31:57.095814434+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"fsxeee3c"}
{"time":"2025-03-06T23:31:57.096198791+08:00","level":"INFO","msg":"stream: closed","id":"fsxeee3c"}
