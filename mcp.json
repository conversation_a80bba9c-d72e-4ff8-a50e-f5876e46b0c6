{"mcpServers": {"model_scope_fetch": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/2084ab49e71342/sse"}, "amap-maps": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/15fafdb1869546/sse"}, "github.com/upstash/context7-mcp": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "github.com/AgentDeskAI/browser-tools-mcp": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp@latest"], "disabled": false, "autoApprove": []}, "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/workspaces/pytest"], "disabled": false, "autoApprove": []}}}