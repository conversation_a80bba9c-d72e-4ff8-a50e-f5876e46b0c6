{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'properties': {'id': {'default': 2, 'title': 'Id', 'type': 'integer'},\n", "  'name': {'default': 'one', 'title': 'Name', 'type': 'string'}},\n", " 'title': 'User',\n", " 'type': 'object'}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["from pydantic import BaseModel\n", "\n", "\n", "class User(BaseModel):\n", "    id : int = 2\n", "    name : str = \"one\"\n", "\n", "user = User(id=4)\n", "user.id = 100\n", "user.model_dump()\n", "user.model_json_schema()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a = 1\n", "b = 2\n", "kwargs = {'c': 3, 'd': 4}\n"]}], "source": ["from typing import Any\n", "\n", "\n", "def example_func(a, b, **kwargs: Any):\n", "    print(\"a =\", a)\n", "    print(\"b =\", b)\n", "    print(\"kwargs =\", kwargs)\n", "    \n", "\n", "example_func(a=1, b=2, c=3, d=4)\n"]}], "metadata": {"kernelspec": {"display_name": "manus", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}